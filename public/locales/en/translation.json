{"sidebar": {"dashboard": "Dashboard", "leads": "Leads", "team": "Team", "integrations": "Integrations", "metaBusiness": "Meta Business Suite", "reports": "Reports", "rolesPermissions": "Roles & Permissions", "marketing": "Marketing", "privacyPolicy": "Privacy Policy", "accountSettings": "Account <PERSON><PERSON>", "termsConditions": "Terms & Conditions", "mainMenu": "Main Menu", "usefulLinks": "Useful Links", "packages": "Packages", "subscription": "Subscription", "support": "Support", "allLeads": "All Leads", "followUpLeads": "Follow Up Leads", "ads": "Ads", "campaigns": "Campaigns"}, "common": {"page": "Page", "export": "Export", "backToAll": "Back To All", "submit": "Submit", "loading": "Loading", "noData": "No data available", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "close": "Close", "back": "Back", "view": "View", "search": "Search", "select": "Select", "filter": "Filter", "actions": "Actions", "id": "ID", "options": "Options", "status": "Status", "date": "Date", "time": "Time", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "description": "Description", "type": "Type", "category": "Category", "price": "Price", "quantity": "Quantity", "total": "Total", "clearingCache": "Clearing...", "cacheCleared": "Cleared", "noResults": "No results found", "integratedSuccessfully": "Integrated successfully", "ownedBy": "Owned by", "of": "of", "apply": "Apply", "clear": "Clear", "from": "From", "to": "To", "all": "All", "upgradeNow": "Upgrade Now", "clearSearch": "Clear search", "notAuthorized": "Not Authorized To View Team Members"}, "header": {"userGuide": "User Guide", "support": "Support", "logout": "Logout", "login": "<PERSON><PERSON>", "profile": "Profile"}, "leadProfile": {"overview": "Overview", "notes": "Notes", "createdBy": "Created By", "lastActivity": "Last Activity", "activities": "Activities", "additionalData": "Additional Data", "noAdditionalData": "No additional data available.", "filePreview": "File Preview:", "filePreviewNotSupported": "File preview not supported for this format.", "downloadFile": "Download the file", "dragDropFile": "Drag 'n' drop a file here, or click to select a file", "noImageSelected": "No image or document selected", "createdDate": "Created date", "leadStatus": "Lead status", "pageName": "Page name", "formName": "Form name", "latestActivity": "Latest activity", "aboutThisContact": "About this contact", "deleteConfirmation": "Are you sure you want to delete this lead?", "assignedTo": "Assigned To", "assignClient": "Assign Client", "assignDesc": "Select a team member to assign this client to", "call": "Call", "message": "Message", "meeting": "Meeting", "activityDetails": "Activity Details", "noImage": "No image or document selected", "resetLead": "Reset Lead", "resetLeadTooltip": "Reset this lead to its original state"}, "forms": {"name": "Name", "emailAddress": "Email Address", "phoneNumber": "Phone Number", "status": "Status", "selectStatus": "Select Status", "amount": "Amount", "quotationAmount": "Quotation amount", "uploadQuotation": "Upload Quotation Offer", "selectQuotationStatus": "Select Quotation Status", "service": "Service", "selectService": "Select Service", "addRejectionReason": "Add Rejection Reason", "refuseReason": "Refuse Reason", "addResult": "Add Result", "selectDateTime": "Select Date and Time", "uploadProof": "Upload A Proof", "addNote": "Add Note", "subject": "Subject", "message": "Message", "leaveComment": "Leave a comment here", "newPassword": "New Password", "confirmPassword": "Confirm Password", "password": "Password", "rememberMe": "Remember Me", "enterRoleName": "Enter Role Name", "selectRole": "Select Role", "typeMessage": "Type message...", "attachFile": "Attach file", "placeholder": {"name": "Name", "email": "Enter your email", "phone": "Phone Number", "amount": "Amount", "result": "Result...", "note": "Note...", "typeNote": "Start typing to leave a note...", "password": "Password", "newPassword": "Enter new password", "confirmPassword": "Confirm new password", "searchIntegrations": "Search Integrations", "emailOrPhone": "Email or Phone Number", "typeMessage": "type message..."}, "messages": {"noChanges": "No changes detected", "leadUpdated": "Lead updated successfully", "updateError": "Error updating the lead"}, "validation": {"nameRequired": "Please enter the lead's name", "validEmail": "Please enter a valid email", "statusRequired": "Please select the lead's status", "amountRequired": "Amount is required", "quotationAmountRequired": "Quotation amount is required", "quotationStatusRequired": "Quotation status is required", "refuseReasonRequired": "Refuse reason is required", "quotationOfferRequired": "Quotation offer is required", "emailRequired": "Email is required"}}, "buttons": {"save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "close": "Close", "view": "View", "submit": "Submit", "search": "Search", "filter": "Filter", "reset": "Reset", "create": "Create", "update": "Update", "download": "Download", "upload": "Upload", "preview": "Preview", "next": "Next", "previous": "Previous", "back": "Back"}, "meeting": {"title": "Title", "host": "Host", "startTime": "Start Time", "endTime": "End Time", "duration": "Duration", "attendees": {"johnDoe": "<PERSON>", "janeSmith": "<PERSON>"}, "location": "Location", "address": "Address", "scheduledReminder": "Scheduled reminder email", "attendeeDescription": "Attendee description", "description": "Send a description to your attendees...", "enterHost": "Enter event host", "enterTitle": "Enter event title", "durations": {"thirtyMin": "30 minutes", "oneHour": "1 hour", "oneHourThirty": "1 hour 30 minutes"}, "locations": {"mainStreet": "123 Main Street, Cityville", "oakAvenue": "456 Oak Avenue, Townsville"}, "rooms": {"conferenceRoomA": "Conference Room A", "conferenceRoomB": "Conference Room B"}, "calendarView": {"today": "Today", "week": "Week", "day": "Day", "month": "Month"}}, "team": {"leadsAssignment": "Leads Assignment", "assignUnassigned": "Assign unassigned leads to team members", "basedOnWorkload": "based on their current workload", "editDataFor": "Edit Data for", "statistics": "Statistics for", "backToTeams": "Back To Teams", "deleteConfirmation": "Are you sure you want to delete this Team Member?", "columns": {"id": "ID", "name": "Name", "status": "Status", "role": "Role", "actions": "Actions"}, "roles": {"admin": "Admin", "moderator": "Moderator", "sales": "Sales", "accountant": "Accountant", "teamMember": "Team Member"}, "actions": {"resendInvite": "Resend Invite", "editPermission": "Edit Permission", "cancel": "Cancel"}, "deleteModal": {"title": "Are you sure you want to delete this Team Member?"}, "toast": {"updateSuccess": "Team member updated successfully", "updateError": "Error updating team member"}, "quotaExceeded": "You've reached the limit of adding {{limits}} members. Subscribe to {{plan}} plan!"}, "dashboard": {"leadsStatusYear": "Leads Status Over The Year", "leadSource": "Lead Source", "salesFunnel": "Sales Funnel", "topTeamMembers": "Top Team Members", "loginRequired": "Please login to view the dashboard"}, "import": {"dragDrop": "Drag 'n' drop a file here", "pleaseUploadFile": "Please upload a file", "pleaseUploadValidExcel": "Please upload a valid Excel file", "selectFileToImport": "Select a file to import", "onlyAcceptFiles": "Only accept .xls, .xlsx, .csv files", "autoAssignLeads": "Auto assign leads"}, "subscription": {"requiredModal": {"title": "Subscription Required", "message": "You need to subscribe to a package to use the system.", "subMessage": "Please choose a subscription package to continue using all features.", "viewPackages": "View Packages"}, "title": "Subscriptions", "subheading": "Lorem ipsum dolor sit amet, consectetur adipiscing elit", "paymentSuccess": "Payment Successful!", "paymentSuccessMessage": "Thank you for your purchase. You'll be redirected shortly.", "paymentFailed": "Payment Failed", "paymentFailedMessage": "We're sorry, but something went wrong. You'll be redirected shortly to try again.", "invoices": {"details": "View and manage your subscription invoices", "currentPlan": "Current Plan", "package": "Package", "amount": "Amount", "duration": "Duration", "months": "months", "paymentTerms": "Payment Terms", "renewalDate": "Renewal Date", "endDate": "End Date", "taxInvoices": "Tax Invoices", "taxInvoiceNumber": "Tax Invoice Number", "noInvoices": "No invoices available", "renew": "Renew Subscription", "cancel": "Cancel Subscription"}}, "marketing": {"totalSpend": "Total Spend", "totalClicks": "Total Clicks", "totalReach": "Total Reach", "averageCPM": "Average CPM", "spend": "Spend", "clicks": "<PERSON>licks", "reach": "Reach", "cpm": "CPM", "socialPlatforms": "Social Platforms", "pages": "Pages", "campaigns": "Campaigns", "adSets": "Ad Sets", "ads": "Ads", "noData": "No data available", "performanceTitle": "Marketing Performance"}, "integrations": {"backToIntegrations": "Back To Integrations", "facebookOptions": "Facebook Integration Options", "tiktokOptions": "Tiktok Integration Options", "getLeadsForm": "Get Leads Form", "searchIntegrations": "Search Integrations", "facebook": {"title": "Facebook Integration Options", "searchPlaceholder": "Search Integrations", "addAccount": "Add Account", "account": "Facebook Account", "connected": "Connected", "relatedPages": "Related Pages", "disconnect": "Disconnect", "confirmDisconnect": "Are you sure you want to disconnect this account?", "pages": {"getLeads": "Get Leads", "showForms": "Show Forms", "leadsImported": "Leads Imported successfully"}, "forms": {"title": "Forms For Each Page", "formsFound": "{{count}} lead forms found", "selectPage": "Select a page to view forms", "form": "Form {{number}}", "getLeadsForm": "Get Leads Form", "inactive": "Inactive Form"}, "description": "Receive new leads from Facebook and Instagram lead ads in your DV Connect account", "reintegrate": "Reintegrate Facebook"}, "tiktok": {"title": "Tiktok Integration Options", "description": "Receive new leads from Tiktok lead ads in your DV Connect account"}, "viewIntegration": "View Integration", "cancelIntegration": "Cancel Integration", "connectTo": "Connect to"}, "meta": {"start": "Start", "end": "End", "conversations": "Conversations", "cost": "Cost", "noResults": "No results found for the selected date range", "whatsappAccount": "WhatsApp account ID"}, "account": {"settings": "Account <PERSON><PERSON>"}, "auth": {"login": "<PERSON><PERSON>", "signUp": {"title": "Create Account", "subtitle": {"line1": "Enter your personal details", "line2": "and start journey with us"}, "name": "Name", "email": "Email", "phone": "Phone Number", "password": "Password", "confirmPassword": "Confirm Password", "terms": "I agree to the Terms and Privacy", "button": "Sign Up", "haveAccount": "Already have an account?"}, "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "backToLogin": "Back to login", "backToEmail": "Back to email", "enterOTP": "Enter OTP", "resendOTP": "Resend OTP", "messages": {"otpSent": "OTP sent to your email", "resetPasswordInstructions": "We will send you a message to set or reset your new password", "enterVerificationCode": "Enter the verification code sent to {{email}}", "passwordResetSuccess": "Password reset successful", "passwordResetError": "Failed to reset password", "otpResent": "OTP resent"}, "validation": {"otpLength": "OTP must be 6 digits", "otpRequired": "OTP is required", "emailRequired": "Email is required"}, "signIn": {"title": "Sign In", "subtitle": {"line1": "To keep connected with us", "line2": "please login with your personal info"}, "email": "Email or Phone Number", "password": "Password", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password?", "button": "Sign In", "orUseEmail": "Or use your email account", "createAccount": "Create Account", "verificationCode": "Verification Code"}, "overlay": {"welcome": {"title": "Welcome Back!", "message": "To keep connected with us please login with your personal info", "button": "Sign In"}, "welcomeDv": {"title": "Welcome to DvConnect!", "message": "Enter your personal details and start your journey with us", "button": "Sign Up"}}, "signInSignUp": {"validation": {"nameRequired": "Name is required", "emailRequired": "Email is required", "emailInvalid": "Invalid email format", "phoneRequired": "Phone number is required", "phoneInvalid": "Invalid phone number", "passwordRequired": "Password is required", "passwordMin": "Password must be at least 8 characters", "passwordsMustMatch": "Passwords must match", "confirmPasswordRequired": "Confirm password is required", "termsRequired": "You must accept the terms and conditions", "usernameRequired": "Username is required"}, "terms": {"iAgree": "I agree with", "and": "and"}, "signIn": {"title": "Sign In", "subtitle": {"line1": "To keep connected with us", "line2": "please login with your personal info"}, "form": {"emailPhone": "Email or Phone Number", "password": "Password", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password?", "button": "Sign In", "orUseEmail": "Or use your email account", "createAccount": "Create Account", "socialLogin": "Continue with social media", "usernameHint": "Email or Phone Number"}}, "signUp": {"title": "Create Account", "subtitle": {"line1": "Enter your personal details", "line2": "and start journey with us"}, "form": {"name": "Name", "email": "Email", "phone": "Phone Number", "password": "Password", "confirmPassword": "Confirm Password", "terms": "I agree to the Terms and Privacy", "button": "Sign Up", "haveAccount": "Already have an account?", "socialSignup": "Sign up with social media"}}, "overlay": {"welcome": {"title": "Welcome Back!", "message": "To keep connected with us please login with your personal info", "button": "Sign In"}, "welcomeDv": {"title": "Welcome to DvConnect!", "message": "Enter your personal details and start your journey with us", "button": "Sign Up"}}, "social": {"facebook": "Continue with Facebook", "google": "Continue with Google", "tiktok": "Continue with TikTok"}}, "otp": {"enter": "Enter OTP"}}, "help": {"helpCenter": "Help Center"}, "roles": {"rolePermissions": "Roles & Permissions", "selectRole": "Select Role", "enterRoleName": "Enter Role Name", "admin": "Admin", "moderator": "Moderator", "sales": "Sales", "accountant": "Accountant", "teamMember": "Team Member", "updateRole": "Update Role", "createRole": "Create Role", "cancel": "Cancel", "roleName": "Role Name", "enterRole": "Enter Role Name"}, "privacy": {"title": "Privacy Policy", "introduction": "Introduction", "overview": "Overview", "overviewDetails": "This Privacy Policy describes how DV Connect (\"we\", \"our\", or \"us\") collects, uses, and discloses your personal information when you visit our website, use our application, or engage with our services.", "lastUpdated": "Last Updated: May 15, 2024", "informationWeCollect": "Information We Collect", "personalData": "Personal Data", "personalDataDetails": "We may collect the following types of personal information:\n• Contact information (name, email address, phone number)\n• Account credentials\n• Profile information\n• Business information\n• Payment information\n• Device and usage information\n• Location data\n• Communications and feedback", "automaticData": "Information Collected Automatically", "automaticDataDetails": "When you use our services, we automatically collect certain information, including:\n• Log data\n• Device information\n• Usage information\n• Cookies and similar technologies", "howWeUseInfo": "How We Use Your Information", "weUseCollectedInfo": "We use the collected information to:", "useDetails": "• Provide, maintain, and improve our services\n• Process transactions and manage your account\n• Send you technical notices, updates, security alerts, and administrative messages\n• Respond to your comments, questions, and requests\n• Communicate with you about products, services, offers, and events\n• Monitor and analyze trends, usage, and activities\n• Detect, prevent, and address technical issues\n• Protect against harmful or illegal activity", "howWeShareInfo": "How We Share Your Information", "shareDetails": "We may share your personal information with:\n• Service providers who perform services on our behalf\n• Business partners with your consent\n• In response to legal requirements\n• In connection with a merger, sale, or acquisition\n• With your consent or at your direction", "dataSecurity": "Data Security", "securityDetails": "We implement appropriate technical and organizational measures to protect the security of your personal information. However, please note that no method of transmission over the Internet or electronic storage is 100% secure.", "yourRights": "Your Data Protection Rights", "rightsDetails": "Depending on your location, you may have the following rights:\n• Access to your personal information\n• Correction of inaccurate data\n• Deletion of your data\n• Restriction of processing\n• Data portability\n• Objection to processing\n• Withdrawal of consent", "exerciseRights": "To exercise these rights, please contact us using the information provided in the 'Contact Us' section.", "changes": "Changes to This Privacy Policy", "changesDetails": "We may update this Privacy Policy from time to time. The updated version will be indicated by an updated \"Last Updated\" date and the updated version will be effective as soon as it is accessible.", "contactUs": "Contact Us", "contactDetails": "If you have any questions about this Privacy Policy, please contact us at:\n• Email: <EMAIL>\n• Phone: ******-567-8900"}, "terms": {"overview": "Overview", "title": "Terms and Conditions", "introduction": "By accessing or using DV Connect's website, application, or services, you agree to be bound by these Terms and Conditions.", "lastUpdated": "Last Updated: May 15, 2024", "acceptance": "Acceptance of Terms", "acceptanceDetails": "By accessing or using our services, you acknowledge that you have read, understood, and agree to be bound by these Terms. If you do not agree with any part of these Terms, you must not use our services.", "accountResponsibilities": "Account Responsibilities", "accountDetails": "When you create an account, you must provide accurate and complete information. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account. You must immediately notify us of any unauthorized use of your account.", "userConduct": "User Conduct", "conductDetails": "You agree not to:\n• Violate any applicable laws or regulations\n• Infringe upon the rights of others\n• Submit false or misleading information\n• Engage in any activity that interferes with or disrupts our services\n• Attempt to gain unauthorized access to our systems or networks\n• Use our services for any illegal or unauthorized purpose", "intellectualProperty": "Intellectual Property", "propertyDetails": "All content, features, and functionality of our services, including but not limited to text, graphics, logos, icons, and software, are the exclusive property of DV Connect and are protected by copyright, trademark, and other intellectual property laws.", "disclaimers": "Disclaimers", "disclaimersDetails": "Our services are provided on an 'as is' and 'as available' basis. We make no warranties, express or implied, regarding the reliability, availability, or accuracy of our services.", "limitation": "Limitation of Liability", "limitationDetails": "To the maximum extent permitted by law, in no event shall DV Connect be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.", "changes": "Changes to Terms", "changesDetails": "We reserve the right to modify these Terms at any time. The updated version will be effective as soon as it is accessible. It is your responsibility to review these Terms periodically.", "termination": "Termination", "terminationDetails": "We may terminate or suspend your account and access to our services at our sole discretion, without notice, for conduct that we believe violates these Terms or is harmful to other users, us, or third parties, or for any other reason.", "governingLaw": "Governing Law", "lawDetails": "These Terms shall be governed by and construed in accordance with the laws of [Country/State], without regard to its conflict of law principles.", "contact": "Contact Information", "contactDetails": "If you have any questions about these Terms, please contact us at:\n• Email: <EMAIL>\n• Phone: ******-567-8900"}, "redirect": {"redirectPage": "Redirect Page", "tiktokIntegrated": "TikTok Integrated Successfully", "loginRequired": "No access token found, you need to login first"}, "client": {"deleteConfirmation": "Are you sure you want to delete this Client?"}, "tables": {"headers": {"id": "ID", "name": "Name", "leadName": "Lead Name", "clientName": "Client", "phone": "Phone", "email": "Email", "emailAddress": "Email Address", "status": "Status", "source": "Source", "createdAt": "Created At", "assignedTo": "Assigned To", "lead": "Lead", "amount": "Amount", "inProgress": "In Progress", "completed": "Completed", "rejected": "Rejected", "member": "Member", "role": "Role", "totalLeads": "Total Leads", "pageName": "Page Name", "formName": "Form Name", "createdBy": "Created By", "employeeName": "Employee Name", "contactNumber": "Contact Number", "gender": "Gender", "location": "Location", "totalClients": "Total Clients", "createdTeamMembers": "Created Team Members", "completedLeads": "Completed Leads", "topRatedTeamMember": "Top Rated Team Member", "mostActiveClient": "Most Active Client", "booked": "Booked", "actions": "Actions", "assigned": "Assigned", "ticketTitle": "Ticket Title", "priority": "Priority", "reassign": "Reassign", "inprogress": "In Progress", "wrong_lead": "Wrong Lead", "not_qualified": "Not Qualified", "no_communication": "No Communication", "canceled": "Canceled", "service": "Lead Service"}, "titles": {"employees": "Employees", "statistics": "Statistics", "reports": "Reports", "leads": "Leads", "teamMembers": "Team Members"}, "status": {"pending": "Pending", "assigned": "Assigned", "inProgress": "In Progress", "completed": "Completed", "rejected": "Rejected", "wrongLead": "Wrong Lead", "notQualified": "Not Qualified", "noCommunication": "No Communication", "booked": "Booked", "bookedReserved": "Booked and Reserved", "canceled": "Canceled", "quotation": "Quotation", "unknown": "Unknown", "active": "Active", "onHold": "On Hold", "undefined": "Undefined", "advancepaid": "Advance Paid", "followup": "Follow Up", "notinterested": "Not Interested", "junk": "Junk", "complaints": "<PERSON><PERSON><PERSON><PERSON>", "urgentcall": "Urgent Call", "callback": "Call Back", "bookedaction2": "Booked", "sentsms": "Sent SMS", "noaction": "No Action", "notinterestedaction2": "Not interested", "whatapp-nal": "Whatapp-NAL", "followupaction2": "Follow up", "complainaction2": "<PERSON><PERSON><PERSON>", "complain": "<PERSON><PERSON><PERSON>", "noanswer": "No Answer"}}, "reports": {"title": "Reports", "quotaExceeded": "Can only view Lead Assignment reports to unlock more...", "tabs": {"teamMembers": "Team Members", "leadAssignment": "Lead Assignment", "salesPerformance": "Sales Performance", "departmentSources": "Department by Sources", "departmentStatuses": "Department by Statuses"}, "errors": {"fetchTeams": "Error fetching team members data", "fetchData": "Error fetching data", "export": "Error exporting data"}, "departmentBySource": "Department Reports by Lead Source", "departmentByStatus": "Department Reports by Lead Status", "exportSuccess": "Data exported successfully", "charts": {"team": {"title": "Team", "topMembers": "Top 5 Members This Week", "leads": "Leads", "noData": "No data available"}, "leadAssignment": {"title": "Lead Assignment", "source": "Lead Source", "status": "Lead Status"}}}, "filters": {"selectSource": "Select Source", "selectTeamMember": "Select Team Member", "selectStatus": "Select Status", "from": "From", "to": "To", "apply": "Apply", "clear": "Clear", "export": "Export", "searchRecords": "Search {{count}} records...", "dateRange": "Date Range", "filterBy": "Filter <PERSON>", "recordsPerPage": "Records per page", "showing": "Showing", "of": "of", "entries": "entries", "custom": "Custom Range", "selectService": "Select Service", "dateRanges": {"today": "Today", "yesterday": "Yesterday", "thisWeek": "This Week", "lastWeek": "Last Week", "thisMonth": "This Month", "lastMonth": "Last Month", "thisYear": "This Year", "custom": "Custom Range"}}, "status": {"pending": "Pending", "assigned": "Assigned", "inProgress": "In Progress", "completed": "Completed", "rejected": "Rejected", "wrongLead": "Wrong Lead", "notQualified": "Not Qualified", "noCommunication": "No Answer", "booked": "Booked", "bookedReserved": "Booked and Reserved", "canceled": "Canceled", "unknown": "Unknown", "quotation": "Quotation", "active": "Active", "onHold": "On Hold"}, "teamMembers": {"selectRole": "Select Role", "selectTeamMember": "Select Team Member", "roles": {"moderator": "Moderator", "sales": "Sales"}, "filters": {"from": "From", "to": "To", "apply": "Apply", "clear": "Clear", "export": "Export"}, "noDataMessage": "No data available for the selected filters.", "searchRecords": "Search {{count}} records...", "searchRecordsCount": "{{count}} records..."}, "leadsTable": {"columns": {"id": "ID", "contactName": "Contact Name", "leadName": "Lead Name", "clientName": "Client Name", "phone": "Phone", "assignedTo": "Assigned To", "source": "Source", "status": "Status", "service": "Service", "pageName": "Page Name", "lastActivity": "Last Activity", "createdAt": "Created At", "actions": "Actions", "noTeamMembers": "No team members available", "leadsfrom": "Leads from", "backToAll": "Back To All", "all": "All", "reassign": "Reassign", "inprogress": "In Progress", "rejected": "Rejected", "wrong_lead": "Wrong Lead", "not_qualified": "Not Qualified", "no_communication": "No Communication", "canceled": "Canceled", "assigned": "Assigned", "booked": "Booked"}, "quotaExceeded": "You've reached the maximum number of leads. Subscribe to unlock more!", "newLeadsTitle": "New Leads Today", "completedLeadsTitle": "Completed Leads", "noLeadsFound": "No leads found"}, "pageHeaders": {"allLeads": "All Leads", "teamMembers": "Team Members", "integrations": "Integrations", "rolesPermissions": "Roles & Permissions", "marketingDetails": "Marketing Details", "followUpLeads": "Follow Up Leads"}, "calendar": {"buttons": {"today": "Today", "day": "Day", "week": "Week", "month": "Month", "agenda": "Agenda"}, "event": {"noTitle": "No title", "appointmentWith": "has an appointment with", "startTime": "Start Time", "endTime": "End Time", "duration": "Duration", "location": "Location", "description": "Description", "noEvents": "No events to display", "loading": "Loading events...", "quotaExceeded": "You've reached the maximum number of appointments. Subscribe to unlock more!"}, "navigation": {"previous": "Previous", "next": "Next", "view": "View"}, "status": {"pending": "Pending", "inProgress": "In Progress", "completed": "Completed", "rejected": "Rejected", "wrongLead": "Wrong Lead", "notQualified": "Not Qualified", "noCommunication": "No Answer", "booked": "Booked", "bookedReserved": "Booked and Reserved", "canceled": "Canceled", "quotation": "Quotation"}, "header": {"meetingCalendar": "Meeting Calendar", "upcomingMeetings": "Upcoming Meetings", "pastMeetings": "Past Meetings"}, "filters": {"allMembers": "All Members", "dateRange": "Date Range", "statusFilter": "Filter by Status", "memberFilter": "Filter by Member"}}, "tableControls": {"tooltips": {"refresh": "Refresh data", "filter": "Show filter options", "clearFilters": "Clear all filters", "exportData": "Export data", "viewDetails": "View full details", "editRecord": "Edit this record", "deleteRecord": "Delete this record", "assignTo": "Assign to team member", "sortAscending": "Sort ascending", "sortDescending": "Sort descending", "addRecord": "Add new record", "bulkActions": "Bulk actions", "columns": "Customize columns", "importData": "Import data"}, "placeholders": {"searchTable": "Search in", "records": "records", "selectAll": "Select all", "selectDate": "Select date", "selectDateRange": "Select date range", "enterName": "Enter name", "enterEmail": "Enter email", "enterPhone": "Enter phone number", "selectOption": "Select an option", "enterAmount": "Enter amount", "filterByStatus": "Filter by status"}}, "createClient": {"title": "Add New Lead", "labels": {"name": "Lead Name", "phone": "Mobile Number", "email": "Email Address", "service": "Lead Service", "notes": "Additional Notes", "autoAssign": "Auto assign leads", "source": "Source"}, "placeholders": {"name": "Enter Lead Name", "phone": "Enter Mobile Number", "email": "Enter Email Address", "service": "Enter Lead Service", "notes": "Enter Additional Notes"}, "validation": {"nameRequired": "Lead Name is required", "invalidPhone": "Invalid phone number", "contactRequired": "At least one of Mobile Number or Email Address is required", "invalidEmail": "Invalid email address", "sourceRequired": "Lead Source is required"}, "buttons": {"addClient": "Add Client"}}, "accountSettings": {"title": "Contact Info", "changePassword": {"title": "Change Password (Optional)", "newPassword": "New Password", "confirmPassword": "Confirm Password"}, "labels": {"name": "Name", "phoneNumber": "Phone Number", "email": "Email", "role": "Role", "autoAssign": "Auto-assign leads", "emailNotifications": "Email notifications for new leads"}, "help": {"autoAssign": "When enabled, new leads will be automatically assigned to your account.", "emailNotifications": "When enabled, you will receive email notifications for new incoming leads."}, "buttons": {"saveChanges": "Save Changes"}, "validation": {"invalidEmail": "Invalid email format", "emailRequired": "Email is required", "nameRequired": "Name is required", "passwordLength": "Password must be at least 8 characters", "passwordsMustMatch": "Passwords must match"}, "toasts": {"noChanges": "No changes made", "success": "Profile updated successfully"}, "autoAssign": {"enableTitle": "Enable Auto-Assignment", "disableTitle": "Disable Auto-Assignment", "enableConfirm": "Are you sure you want to enable auto-assignment of leads? When enabled, new leads will be automatically assigned to your account.", "disableConfirm": "Are you sure you want to disable auto-assignment of leads? When disabled, new leads will remain unassigned until manually assigned.", "enable": "Enable", "disable": "Disable"}, "emailNotifications": {"enableTitle": "Enable Email Notifications", "disableTitle": "Disable Email Notifications", "enableConfirm": "Are you sure you want to enable email notifications for new leads? When enabled, you will receive email notifications whenever new leads are added to the system.", "disableConfirm": "Are you sure you want to disable email notifications for new leads? When disabled, you will not receive email notifications for new leads.", "enable": "Enable", "disable": "Disable"}}, "inviteMember": {"title": "Invite New Team Member", "labels": {"name": "Name", "email": "Email", "phone": "Phone", "role": "Role", "password": "Password", "confirmPassword": "Confirm Password"}, "placeholders": {"name": "member name", "email": "<EMAIL>", "phone": "phone number", "password": "password", "confirmPassword": "confirm password", "selectRole": "Select Role"}, "validation": {"nameRequired": "Name is required", "emailInvalid": "Invalid email address", "contactRequired": "At least one of Mobile Number or Email Address is required", "passwordRequired": "Password is required", "passwordMin": "Password must be at least 6 characters", "confirmPasswordRequired": "Confirm Password is required", "passwordsMustMatch": "Passwords must match", "roleRequired": "Role is required"}, "buttons": {"add": "Add to Page"}}, "teamMember": {"charts": {"totalTasks": "Total Tasks", "totalFinished": "Total Finished", "totalChallenges": "Total Challenges", "performance": "Performance", "skills": "Skills", "totalLeads": "Total Leads", "status": {"rejected": "Rejected", "inprogress": "In Progress", "completed": "Completed", "pending": "Pending"}}}, "messages": {"leadAssignedSuccess": "Lead assigned successfully"}, "clientsTable": {"newClientsTitle": "New Clients", "columns": {"name": "Name", "email": "Email", "phone": "Phone", "status": "Status", "createdAt": "Created At", "numberOfLeads": "Number Of Leads", "package": "Package"}, "noClientsFound": "No New Clients Found"}, "adsTable": {"title": "Ads Management", "columns": {"id": "ID", "adName": "Ad Name", "campaignName": "Campaign Name", "pageName": "Page Name", "leadsCount": "Leads Count", "actions": "Actions", "backToAds": "Back to Ads"}, "noAdsFound": "No Ads Found", "leadsForAd": "Leads for Ad: {{adName}}"}, "campaignsTable": {"title": "Campaigns Management", "columns": {"id": "ID", "campaignName": "Campaign Name", "adsCount": "Ads Count", "startDate": "Start Date", "status": "Status", "insights": "Insights", "messages": "Messages", "clicks": "<PERSON>licks", "reach": "Reach", "likes": "<PERSON>s", "actions": "Actions"}, "noCampaignsFound": "No Campaigns Found", "adsForCampaign": "Ads for Campaign:"}}