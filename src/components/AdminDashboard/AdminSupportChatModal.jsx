import React, { useEffect, useRef, useState } from "react";
import {
  <PERSON><PERSON>,
  Button,
  Form,
  ListGroup,
  Row,
  Col,
  Badge,
} from "react-bootstrap";
import {
  FaPaperPlane,
  FaPaperclip,
  FaTimes,
  FaArrowLeft,
} from "react-icons/fa";
import {
  collection,
  doc,
  onSnapshot,
  orderBy,
  query,
  writeBatch,
  getDocs,
} from "firebase/firestore";
import { db } from "../../utils/firebase.config";
import { useSelector, useDispatch } from "react-redux";
import "./AdminSupportChatModal.css";
import notificationSound from "../../assets/media/notification_sound.wav";
import { getFileType } from "../../utils/getFileType";
import {
  fetchClientChats,
  setActiveClient,
  markClientMessagesAsRead,
  removeUnreadChat,
  setMessages,
  selectClientChats,
  selectActiveClientId,
  selectActiveClientName,
  selectMessages,
  setupGlobalAdminChatListener,
  sendMessageAsSupport,
  setMessageText,
  setAttachment,
  selectMessageText,
  selectAttachment,
  selectLoading,
  selectUnreadChats,
} from "../../redux/features/adminSupportChatSlice";
import FilePreviewModal from "../../components/Shared/modals/FilePreviewModal/FilePreviewModal";

const AdminSupportChatModal = ({ show, onClose }) => {
  const dispatch = useDispatch();
  const clientChats = useSelector(selectClientChats);
  const activeClientId = useSelector(selectActiveClientId);
  const activeClientName = useSelector(selectActiveClientName);
  const messages = useSelector(selectMessages);
  const messageText = useSelector(selectMessageText);
  const attachment = useSelector(selectAttachment);
  const loading = useSelector(selectLoading);
  const unreadChats = useSelector(selectUnreadChats);
  const user = useSelector((state) => state.auth.user);

  const [searchTerm, setSearchTerm] = useState("");
  const [showClientsList, setShowClientsList] = useState(true);
  const messagesEndRef = useRef(null);
  const messagesRef = useRef(null);
  const notificationSoundRef = useRef(new Audio(notificationSound));
  const prevUnreadChatsRef = useRef([]);
  const isMobileView = window.innerWidth < 768;

  // Add refs for input focus
  const messageInputRef = useRef(null);
  const activeClientRef = useRef(null);

  // Track if the chat is currently focused/active
  const [isChatFocused, setIsChatFocused] = useState(false);

  // Add state to track which messages have their time visible
  const [visibleTimes, setVisibleTimes] = useState({});

  // Function to toggle time visibility for a specific message
  const toggleMessageTime = (messageId) => {
    setVisibleTimes((prev) => ({
      ...prev,
      [messageId]: !prev[messageId],
    }));
  };

  // Reset visible times when changing chats
  useEffect(() => {
    setVisibleTimes({});
  }, [activeClientId]);

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Fetch client chats when modal opens
  useEffect(() => {
    if (show) {
      dispatch(fetchClientChats());
      // Set up global listener if not already set up
      dispatch(setupGlobalAdminChatListener());
      // On mobile, show clients list by default
      if (isMobileView) {
        setShowClientsList(true);
      }
    } else {
      // Reset active client when modal closes
      dispatch(setActiveClient({ id: null, name: null }));
      dispatch(setMessages([]));
      dispatch(setMessageText(""));
      dispatch(setAttachment(null));
      setShowClientsList(true);
    }
  }, [show, dispatch, isMobileView]);

  // Set up listener for messages when active client changes
  useEffect(() => {
    if (show && activeClientId) {
      try {
        // Set up listener for messages
        const chatDocRef = doc(db, "support", activeClientId);
        const messagesCollectionRef = collection(chatDocRef, "messages");
        const messagesQuery = query(
          messagesCollectionRef,
          orderBy("created_time", "asc")
        );

        const unsubscribe = onSnapshot(messagesQuery, (snapshot) => {
          const newMessages = snapshot.docs.map((doc) => {
            const data = doc.data();

            // Convert Firebase Timestamp to serializable format
            if (
              data.created_time &&
              typeof data.created_time.toDate === "function"
            ) {
              return {
                id: doc.id,
                ...data,
                created_time: data.created_time.toDate().toISOString(),
              };
            }

            return {
              id: doc.id,
              ...data,
            };
          });

          // Ensure messages are properly sorted by created_time
          const sortedMessages = [...newMessages].sort((a, b) => {
            const dateA = new Date(a.created_time || a.client_time);
            const dateB = new Date(b.created_time || b.client_time);
            return dateA - dateB; // Ascending order (oldest first)
          });

          dispatch(setMessages(sortedMessages));
        });

        // Mark client messages as read when opening the chat
        dispatch(markClientMessagesAsRead(activeClientId));
        dispatch(removeUnreadChat(activeClientId));

        return () => {
          unsubscribe();
        };
      } catch (error) {
        console.error("Error setting up message listener:", error);
      }
    }
  }, [show, activeClientId, dispatch]);

  // Play notification sound when new unread messages arrive (PAUSED)
  useEffect(() => {
    // Check if there are new unread chats compared to previous state
    const hasNewUnreadChats = unreadChats.some(
      (chatId) => !prevUnreadChatsRef.current.includes(chatId)
    );

    // Only play sound if:
    // 1. There are new unread chats AND
    // 2. Either the modal is not shown OR the chat with new messages is not the active one OR the active chat is not focused
    if (
      hasNewUnreadChats &&
      (!show || !unreadChats.includes(activeClientId) || !isChatFocused)
    ) {
      console.log(
        "🔇 [ADMIN SUPPORT CHAT] Notification sound PAUSED - would have played for new unread chats"
      );
      // PAUSED: Comment out the sound playing
      // notificationSoundRef.current.play().catch((error) => {
      //   console.error("Error playing notification sound:", error);
      // });
    }

    // Update the previous unread chats reference
    prevUnreadChatsRef.current = unreadChats;
  }, [unreadChats, show, activeClientId, isChatFocused]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      const newIsMobileView = window.innerWidth < 768;
      if (newIsMobileView !== isMobileView) {
        window.location.reload();
      }
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [isMobileView]);

  // Maintain focus on message input after sending
  useEffect(() => {
    if (!loading && messageInputRef.current && isChatFocused) {
      messageInputRef.current.focus();
    }
  }, [loading, isChatFocused]);

  // Set up focus tracking for the active chat
  useEffect(() => {
    if (show && activeClientId) {
      setIsChatFocused(true);

      // Focus the message input when a chat is selected
      if (messageInputRef.current) {
        messageInputRef.current.focus();
      }

      // Mark messages as read when the chat is focused
      dispatch(markClientMessagesAsRead(activeClientId));
      dispatch(removeUnreadChat(activeClientId));
    } else {
      setIsChatFocused(false);
    }
  }, [show, activeClientId, dispatch]);

  const [sendingMessages, setSendingMessages] = useState([]);
  const [selectedFile, setSelectedFile] = useState(null);
  const [showFilePreview, setShowFilePreview] = useState(false);
  const [showFilePreviewModal, setShowFilePreviewModal] = useState(false);
  const [previewFile, setPreviewFile] = useState(null);

  // Function to handle file preview
  const handleFilePreview = (file) => {
    // Create a file object with the necessary properties for the FilePreviewModal
    const fileForPreview = {
      url: file.message,
      type: file.type,
      filename: file.filename || "File",
    };

    // Set the file and show the modal
    setPreviewFile(fileForPreview);
    setShowFilePreviewModal(true);
  };

  // Function to add a temporary sending message
  const addSendingMessage = (message) => {
    const tempId = `temp_${Date.now()}`;
    setSendingMessages((prev) => [
      ...prev,
      { ...message, id: tempId, isSending: true },
    ]);
    return tempId;
  };

  // Function to remove a sending message when it's done
  const removeSendingMessage = (id) => {
    setSendingMessages((prev) => prev.filter((msg) => msg.id !== id));
  };

  const handleSendMessage = async (e) => {
    e.preventDefault();

    if (!messageText.trim() && !attachment) return;
    if (!activeClientId) return;

    const supportUserId = user?.user?.id;
    const supportUserName = user?.user?.name || "Support Agent";

    // Create temporary message for UI
    const tempMessage = {
      message: messageText,
      sender_id: supportUserId,
      sender_name: supportUserName,
      flag: "support",
      created_time: new Date().toISOString(),
      type: attachment ? getFileType(attachment) : "text",
      filename: attachment?.name,
    };

    // Add to sending messages
    const tempId = addSendingMessage(tempMessage);

    dispatch(
      sendMessageAsSupport({
        clientId: activeClientId,
        clientName: activeClientName,
        messageText,
        attachment,
        supportUserId,
        supportUserName,
      })
    )
      .then(() => {
        // Remove from sending messages when done
        removeSendingMessage(tempId);
      })
      .catch(() => {
        // Also remove on error
        removeSendingMessage(tempId);
      });

    // Set a short timeout to refocus the input after Redux state updates
    setTimeout(() => {
      if (messageInputRef.current) {
        messageInputRef.current.focus();
      }
    }, 10);
  };

  const handleFileChange = (e) => {
    if (e.target.files[0]) {
      dispatch(setAttachment(e.target.files[0]));
    }
  };

  const removeAttachment = () => {
    dispatch(setAttachment(null));
  };

  const handleSelectClient = (clientId, clientName) => {
    dispatch(setActiveClient({ id: clientId, name: clientName }));

    // Mark client messages as read when selected
    dispatch(markClientMessagesAsRead(clientId));
    dispatch(removeUnreadChat(clientId));

    // Set chat as focused
    setIsChatFocused(true);

    // On mobile, hide clients list when a chat is selected
    if (isMobileView) {
      setShowClientsList(false);
    }

    // Focus the message input after a short delay to allow state updates
    setTimeout(() => {
      if (messageInputRef.current) {
        messageInputRef.current.focus();
      }
    }, 100);
  };

  const toggleClientsList = () => {
    setShowClientsList(!showClientsList);
  };

  const formatTime = (timestamp) => {
    if (!timestamp) return "";

    let date;
    // Handle different timestamp formats
    if (typeof timestamp === "string") {
      // ISO string format
      date = new Date(timestamp);
    } else if (timestamp.toDate && typeof timestamp.toDate === "function") {
      // Firebase Timestamp object
      date = timestamp.toDate();
    } else {
      // Regular Date object or timestamp number
      date = new Date(timestamp);
    }

    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();

    if (isToday) {
      return date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
    } else {
      return (
        date.toLocaleDateString([], {
          month: "short",
          day: "numeric",
        }) +
        " " +
        date.toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        })
      );
    }
  };

  // Function to determine if a message is the latest from its sender
  const isLatestMessageFromSender = (message, index) => {
    if (index === messages.length - 1) return true;

    // Check if the next message is from a different sender
    const nextMessage = messages[index + 1];
    return message.flag !== nextMessage.flag;
  };

  // Filter clients based on search term
  const filteredClients = clientChats.filter((client) => {
    // Make sure client.client_name exists before calling toLowerCase()
    const clientName = client.client_name || "";
    return clientName.toLowerCase().includes(searchTerm.toLowerCase());
  });

  // Sort clients by last message time
  const sortedClients = [...filteredClients].sort((a, b) => {
    if (!a.lastMessageTime) return 1;
    if (!b.lastMessageTime) return -1;
    return new Date(b.lastMessageTime) - new Date(a.lastMessageTime);
  });

  // Add a function to handle focus events
  const handleChatFocus = () => {
    if (activeClientId) {
      setIsChatFocused(true);
      dispatch(markClientMessagesAsRead(activeClientId));
      dispatch(removeUnreadChat(activeClientId));
    }
  };

  // Add a function to handle blur events
  const handleChatBlur = () => {
    setIsChatFocused(false);
  };

  return (
    <>
      <Modal
        show={show}
        onHide={onClose}
        centered
        size="xl"
        className="admin-support-chat-modal"
      >
        <Modal.Header closeButton>
          <Modal.Title>
            {!showClientsList && activeClientId && (
              <Button
                variant="link"
                className="back-button me-2 p-0"
                onClick={toggleClientsList}
              >
                <FaArrowLeft />
              </Button>
            )}
            {activeClientId && !showClientsList
              ? `Chat with ${activeClientName || "Client"}`
              : "Client Support Chats"}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="d-flex px-3 py-2">
          {/* Clients sidebar */}
          {showClientsList && (
            <div className="clients-sidebar">
              <div className="search-container p-2">
                <Form.Control
                  type="text"
                  placeholder="Search clients..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="mb-2"
                />
              </div>
              <ListGroup variant="flush" className="clients-list">
                {sortedClients.length === 0 ? (
                  <div className="text-center text-muted my-3">
                    <p>No clients found</p>
                  </div>
                ) : (
                  sortedClients.map((client) => (
                    <ListGroup.Item
                      key={client.id}
                      action
                      active={client.id === activeClientId}
                      onClick={() =>
                        handleSelectClient(
                          client.id,
                          client.client_name || "Unknown Client"
                        )
                      }
                      className={`client-item ${
                        unreadChats.includes(client.id) ? "has-unread" : ""
                      } ${client.id === activeClientId ? "active" : ""}`}
                      ref={
                        client.id === activeClientId ? activeClientRef : null
                      }
                    >
                      <div className="d-flex justify-content-between align-items-center">
                        <div>
                          <div className="client-name">
                            {client.client_name || "Unknown Client"}
                          </div>
                          <div
                            className={`last-message text-truncate ${
                              client.lastMessageSender === "support"
                                ? "text-primary"
                                : ""
                            }`}
                          >
                            {client.lastMessageSender === "support"
                              ? "You: "
                              : ""}
                            {client.latest_message || "No messages yet"}
                          </div>
                        </div>
                        <div className="d-flex flex-column align-items-end">
                          <small className="text-muted">
                            {client.lastMessageTime
                              ? formatTime(client.lastMessageTime)
                              : ""}
                          </small>
                          {unreadChats.includes(client.id) && (
                            <Badge bg="danger" pill className="mt-1">
                              New
                            </Badge>
                          )}
                        </div>
                      </div>
                    </ListGroup.Item>
                  ))
                )}
              </ListGroup>
            </div>
          )}

          {/* Chat area with focus handling */}
          <div
            className={`chat-area ${!showClientsList ? "full-width" : ""}`}
            onFocus={handleChatFocus}
            onBlur={handleChatBlur}
            tabIndex="-1" // Make div focusable but not in tab order
          >
            {!activeClientId ? (
              <div className="text-center text-muted my-5">
                <p>Select a client to view their chat</p>
              </div>
            ) : (
              <>
                <div className="chat-messages-container">
                  {messages.length === 0 && sendingMessages.length === 0 ? (
                    <div className="text-center text-muted my-5">
                      <p>No messages yet</p>
                    </div>
                  ) : (
                    <>
                      {/* Render actual messages from Firebase */}
                      {messages.map((msg, index) => (
                        <div
                          key={msg.id}
                          className={`message-container ${
                            msg.flag === "support"
                              ? "message-right"
                              : "message-left"
                          }`}
                        >
                          <div
                            className={`message-bubble ${
                              msg.flag === "support"
                                ? "support-message"
                                : "client-message"
                            }`}
                            onClick={() => toggleMessageTime(msg.id)}
                          >
                            <div className="message-content">
                              {(() => {
                                // Check if message has a type property and it's not 'text'
                                if (msg.type && msg.type !== "text") {
                                  if (msg.type === "image") {
                                    return (
                                      <img
                                        src={msg.message}
                                        alt="Image"
                                        className="message-image"
                                        style={{
                                          maxWidth: "200px",
                                          maxHeight: "200px",
                                          cursor: "pointer",
                                        }}
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleFilePreview(msg);
                                        }}
                                        onError={(e) => {
                                          console.error(
                                            "Image failed to load:",
                                            e.target.src
                                          );
                                          e.target.onerror = null;
                                          e.target.style.display = "none";
                                          e.target.parentNode.textContent =
                                            "Image could not be loaded";
                                        }}
                                      />
                                    );
                                  } else if (
                                    msg.type === "file" ||
                                    msg.type === "document" ||
                                    msg.type === "pdf"
                                  ) {
                                    return (
                                      <a
                                        href="#"
                                        className="file-attachment"
                                        onClick={(e) => {
                                          e.preventDefault();
                                          e.stopPropagation();
                                          handleFilePreview(msg);
                                        }}
                                      >
                                        📎 {msg.filename || "View file"}
                                      </a>
                                    );
                                  } else if (msg.type === "audio") {
                                    return (
                                      <audio
                                        controls
                                        className="message-audio"
                                        onClick={(e) => e.stopPropagation()}
                                      >
                                        <source
                                          src={msg.message}
                                          type="audio/mpeg"
                                        />
                                        Your browser does not support the audio
                                        element.
                                      </audio>
                                    );
                                  } else if (msg.type === "video") {
                                    return (
                                      <video
                                        controls
                                        className="message-video"
                                        style={{
                                          maxWidth: "200px",
                                          maxHeight: "200px",
                                          cursor: "pointer",
                                        }}
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleFilePreview(msg);
                                        }}
                                      >
                                        <source
                                          src={msg.message}
                                          type="video/mp4"
                                        />
                                        Your browser does not support the video
                                        element.
                                      </video>
                                    );
                                  }
                                }

                                // Default case: text message or unknown type
                                return (
                                  <span style={{ whiteSpace: "pre-wrap" }}>
                                    {msg.message}
                                  </span>
                                );
                              })()}
                            </div>
                            {visibleTimes[msg.id] && (
                              <div className="message-time">
                                {formatTime(msg.created_time)}
                              </div>
                            )}
                          </div>
                          {/* Only show sender name if it's the latest message from this sender */}
                          {isLatestMessageFromSender(msg, index) && (
                            <div className="message-sender">
                              {msg.sender_name}
                            </div>
                          )}
                        </div>
                      ))}

                      {/* Render sending messages with loading state */}
                      {sendingMessages.map((msg) => (
                        <div
                          key={msg.id}
                          className="message-container message-right"
                        >
                          <div
                            className="message-bubble support-message"
                            style={{
                              opacity: 0.7,
                              position: "relative",
                            }}
                          >
                            <div className="message-content">
                              {(() => {
                                if (msg.type === "image") {
                                  return (
                                    <div className="position-relative">
                                      {/* Show a placeholder for image uploads */}
                                      <div
                                        className="image-placeholder"
                                        style={{
                                          width: "200px",
                                          height: "150px",
                                          background: "#f0f0f0",
                                          display: "flex",
                                          alignItems: "center",
                                          justifyContent: "center",
                                          borderRadius: "8px",
                                          filter: "blur(1px)",
                                        }}
                                      >
                                        <span>Uploading image...</span>
                                      </div>
                                      <div className="position-absolute top-50 start-50 translate-middle">
                                        <div
                                          className="spinner-border spinner-border-sm text-light"
                                          role="status"
                                        >
                                          <span className="visually-hidden">
                                            Sending...
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                                  );
                                } else if (
                                  msg.type === "file" ||
                                  msg.type === "document"
                                ) {
                                  return (
                                    <div className="position-relative">
                                      <a
                                        className="file-attachment"
                                        style={{ filter: "blur(1px)" }}
                                      >
                                        📎 {msg.filename || "Uploading file..."}
                                      </a>
                                      <div className="position-absolute top-50 start-50 translate-middle">
                                        <div
                                          className="spinner-border spinner-border-sm text-light"
                                          role="status"
                                        >
                                          <span className="visually-hidden">
                                            Sending...
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                                  );
                                } else {
                                  return (
                                    <div className="position-relative">
                                      <span
                                        style={{
                                          whiteSpace: "pre-wrap",
                                          filter: "blur(1px)",
                                        }}
                                      >
                                        {msg.message}
                                      </span>
                                      <div className="position-absolute top-50 start-50 translate-middle">
                                        <div
                                          className="spinner-border spinner-border-sm text-light"
                                          role="status"
                                        >
                                          <span className="visually-hidden">
                                            Sending...
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                                  );
                                }
                              })()}
                            </div>
                          </div>
                          <div className="message-sender">
                            {msg.sender_name}
                          </div>
                        </div>
                      ))}
                    </>
                  )}
                  <div ref={messagesEndRef} />
                </div>

                <Form
                  onSubmit={handleSendMessage}
                  className="message-input-container mt-3"
                >
                  {attachment && (
                    <div className="attachment-preview">
                      <span>{attachment.name}</span>
                      <Button
                        variant="link"
                        onClick={removeAttachment}
                        className="p-0 ms-2"
                      >
                        <FaTimes />
                      </Button>
                    </div>
                  )}

                  <div className="d-flex">
                    <Form.Group className="me-2 position-relative flex-grow-1">
                      <Form.Control
                        ref={messageInputRef}
                        type="text"
                        placeholder="Type your message..."
                        value={messageText}
                        onChange={(e) =>
                          dispatch(setMessageText(e.target.value))
                        }
                        disabled={loading}
                        className="message-input"
                        onFocus={() => {
                          if (activeClientId) {
                            dispatch(markClientMessagesAsRead(activeClientId));
                            dispatch(removeUnreadChat(activeClientId));
                          }
                        }}
                      />
                    </Form.Group>

                    <label className="btn btn-outline-secondary me-2 attachment-btn">
                      <FaPaperclip />
                      <input
                        type="file"
                        hidden
                        onChange={handleFileChange}
                        disabled={loading}
                      />
                    </label>

                    <Button
                      type="submit"
                      variant="primary"
                      disabled={loading || (!messageText.trim() && !attachment)}
                    >
                      {loading ? (
                        <span
                          className="spinner-border spinner-border-sm"
                          role="status"
                          aria-hidden="true"
                        ></span>
                      ) : (
                        <FaPaperPlane />
                      )}
                    </Button>
                  </div>
                </Form>
              </>
            )}
          </div>
        </Modal.Body>
      </Modal>
      <FilePreviewModal
        file={selectedFile}
        show={showFilePreview}
        handleClose={() => setShowFilePreview(false)}
      />
      <FilePreviewModal
        show={showFilePreviewModal}
        handleClose={() => setShowFilePreviewModal(false)}
        file={previewFile}
      />
    </>
  );
};

export default AdminSupportChatModal;
