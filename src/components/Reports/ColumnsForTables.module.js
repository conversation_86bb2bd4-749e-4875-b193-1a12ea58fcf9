import { Toolt<PERSON> } from "react-tooltip";
import { format, parseISO } from "date-fns";
import { ReactSVG } from "react-svg";
import { IoSparklesSharp } from "react-icons/io5";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useMemo } from "react";
import { Badge, Button, Form, Spinner, Dropdown } from "react-bootstrap";
import { FaUserPlus, FaPhone, FaEnvelope, FaCalendar } from "react-icons/fa6";
import { MdAssignmentTurnedIn, MdEdit } from "react-icons/md";
import { PiTrashFill } from "react-icons/pi";
import { FaEye, FaUserCog } from "react-icons/fa";
import { BsEye } from "react-icons/bs";
import StatusDropdown from './StatusDropdown';
import PriorityDropdown from './PriorityDropdown';
import { sourceToIcon } from "../../constants/sourceIcons";
import { getLeadStatuses, isUserExcluded } from "../../config/leadStatusConfig";

// Define constants for lead statuses to avoid hardcoding
const LEAD_STATUS = {
  PENDING: 0,
  IN_PROGRESS: 1,
  COMPLETED: 2,
  REJECTED: 3,
  WRONG_LEAD: 4,
  NOT_QUALIFIED: 5,
  NO_COMMUNICATION: 6,
  BOOKED: 7,
  BOOKED_AND_RESERVED: 8,
  CANCELED: 9,
  QUOTATION: 10,
  ASSIGNED: 11
};

// Utility function to get status information based on user role
const getStatusInfo = (statusCode, user, t) => {
  const numericStatus = Number(statusCode);

  // Get userId - try multiple possible paths
  const userId = user?.user?.id || user?.id || user?.user_id;

  const isSpecialUser = isUserExcluded(userId);

  const availableStatuses = getLeadStatuses(userId);

  // Get status text
  const getStatusText = () => {
    if (isSpecialUser) {
      // For special users, only show specific statuses
      switch (numericStatus) {
        case 0: return t('tables.status.pending');
        case 11: return t('tables.status.assigned');
        case 12: return t('tables.status.undefined');
        case 13: return t('tables.status.advancepaid');
        case 14: return t('tables.status.followup');
        case 15: return t('tables.status.notinterested');
        case 16: return t('tables.status.junk');
        case 17: return t('tables.status.complaints');
        case 18: return t('tables.status.urgentcall');
        case 19: return t('tables.status.callback');
        case 20: return t('tables.status.bookedaction2');
        case 21: return t('tables.status.sentsms');
        case 22: return t('tables.status.noaction');
        case 23: return t('tables.status.notinterestedaction2');
        case 24: return t('tables.status.whatapp-nal');
        case 25: return t('tables.status.followupaction2');
        case 26: return t('tables.status.complainaction2');
        case 6: return t('tables.status.noanswer'); // No Answer for special users
        case 7: return t('tables.status.booked'); // Booked for special users
        default: return availableStatuses[numericStatus] || t('tables.status.unknown');
      }
    } else {
      // For regular users, show all basic statuses
      switch (numericStatus) {
        case 0: return t('tables.status.pending');
        case 1: return t('tables.status.inProgress');
        case 2: return t('tables.status.completed');
        case 3: return t('tables.status.rejected');
        case 4: return t('tables.status.wrongLead');
        case 5: return t('tables.status.notQualified');
        case 6: return t('tables.status.noCommunication');
        case 7: return t('tables.status.booked');
        case 8: return t('tables.status.bookedReserved');
        case 9: return t('tables.status.canceled');
        case 10: return t('tables.status.quotation');
        case 11: return t('tables.status.assigned');
        default: return availableStatuses[numericStatus] || t('tables.status.unknown');
      }
    }
  };

  // Get status CSS class
  const getStatusClass = () => {
    if (isSpecialUser) {
      // For special users, use simplified class mapping
      switch (numericStatus) {
        case 0: return 'status-badge--pending';
        case 11: return 'status-badge--in-progress';
        case 6: case 7: case 12: case 13: case 14: case 20: return 'status-badge--booked';
        case 15: case 16: case 23: return 'status-badge--rejected';
        case 17: case 18: case 19: case 26: return 'status-badge--wrong-lead';
        case 21: case 22: case 24: case 25: return 'status-badge--not-qualified';
        default: return 'status-badge--unknown';
      }
    } else {
      // For regular users, use standard class mapping
      switch (numericStatus) {
        case 0: return 'status-badge--pending';
        case 1: case 11: return 'status-badge--in-progress';
        case 2: return 'status-badge--completed';
        case 3: return 'status-badge--rejected';
        case 4: return 'status-badge--wrong-lead';
        case 5: return 'status-badge--not-qualified';
        case 6: return 'status-badge--no-communication';
        case 7: case 8: return 'status-badge--booked';
        case 9: return 'status-badge--canceled';
        case 10: return 'status-badge--quotation-sent';
        default: return 'status-badge--unknown';
      }
    }
  };

  return {
    text: getStatusText(),
    className: getStatusClass(),
    isVisible: availableStatuses.hasOwnProperty(numericStatus)
  };
};

export const createTranslatedColumns = (t, user = null) => {
  const leadAssignmentColumns = [
    {
      Header: "#",
      accessor: "leadId",
      Cell: ({ row }) => <Link to={`/leads/${row.original.id}`}>{row.index + 1}</Link>,
    },
    {
      Header: t('tables.headers.name'),
      accessor: "name",
      Cell: ({ row }) => {
        const name = row.original.name;
        return (
          <>
            <Link
              className={`one-line lead-name-reports${row.original.id}`}
              style={{ maxWidth: "100px" }}
              to={`/leads/${row.original.id}`}>
              {row.original.status === LEAD_STATUS.PENDING ? <IoSparklesSharp className={"mainColor"} /> : null}  {name}
            </Link>
            <Tooltip
              anchorSelect={`.lead-name-reports${row.original.id}`}
              content={name}
              className={"bg-dark text-white"}
            />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.phone'),
      accessor: "phone",
      Cell: ({ row }) => {
        const phone = row.original.phone;
        return (
          <>
            <Link
              to={`/leads/${row.original.id}`}
              className={`one-line lead-phone-reports${row.original.id}`}
              style={{ maxWidth: "100px" }}
            >
              {phone}
            </Link>
            <Tooltip
              anchorSelect={`.lead-phone-reports${row.original.id}`}
              content={phone}
              className={"bg-dark text-white"}
            />
          </>
        );
      }
    },
    {
      Header: t('tables.headers.assignedTo'),
      accessor: "assigned_name",
      Cell: ({ row }) => {
        const assignedName = row.original.assigned_name;
        return (
          <>
            <Link
              to={`/leads/${row.original.id}`}
              className={`one-line assigned-name-reports${row.original.id}`}
              style={{ maxWidth: "150px" }}
            >
              {assignedName}
            </Link>
            <Tooltip
              anchorSelect={`.assigned-name-reports${row.original.id}`}
              content={assignedName}
              className={"bg-dark text-white"}
            />
          </>
        );
      }
    },
    { Header: t('tables.headers.service'), accessor: "service" },
    {
      Header: t('tables.headers.source'),
      accessor: "source",
      Cell: ({ row }) => {
        const source = row.original.source;
        const IconComponent = sourceToIcon[source] || null;

        return (
          <Link to={`/leads/${row.original.id}`} className={"mx-auto social-icon-container"}>
            {IconComponent && <ReactSVG src={IconComponent} />}
          </Link>
        );
      },
    },
    {
      Header: t('tables.headers.createdAt'),
      accessor: "created_at",
      Cell: ({ value, row }) => {
        if (value) {
          const parsedDate = new Date(value);
          if (!isNaN(parsedDate.getTime())) {
            const formattedDate = format(parsedDate, "yyyy-MM-dd HH:mm");
            return (
              <>
                <Link
                  to={`/leads/${row.original.id}`}
                  className={`one-line created-at-reports${row.original.id}`}
                  style={{ maxWidth: "160px" }}
                >
                  {formattedDate}
                </Link>
                <Tooltip
                  anchorSelect={`.created-at-reports${row.original.id}`}
                  content={formattedDate}
                  className={"bg-dark text-white"}
                />
              </>
            );
          }
        }
        return (
          <>
            <Link
              to={`/leads/${row.original.id}`}
              className={`one-line created-at-reports${row.original.id}`}
              style={{ maxWidth: "160px" }}
            >
              {value}
            </Link>
            <Tooltip
              anchorSelect={`.created-at-reports${row.original.id}`}
              content={value}
              className={"bg-dark text-white"}
            />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.status'),
      accessor: "status",
      Cell: ({ row }) => {
        const status = Number(row?.original?.status);
        const statusInfo = getStatusInfo(status, user, t);

        return (
          <>
            <Link
              to={`/leads/${row.original.id}`}
              className={`one-line status-reports${row.original.id} status-badge ${statusInfo.className} rounded-pill p-1 fw-bold`}
              style={{ fontSize: "0.8rem", maxWidth: "150px" }}
            >
              {statusInfo.text}
            </Link>
            <Tooltip
              anchorSelect={`.status-reports${row.original.id}`}
              content={statusInfo.text}
              className={"bg-dark text-white"}
            />
          </>
        );
      },
    },
  ];

  const teamMembersColumns = [
    {
      Header: "#",
      accessor: "teamMemberId",
      Cell: ({ row }) => <span>{row.index + 1}</span>,
    },
    {
      Header: t('tables.headers.member'),
      accessor: "name",
      Cell: ({ row }) => {
        const name = row.original.name;
        return (
          <>
            <div
              className={`one-line tm-name-${row.original.id}`}
              style={{ maxWidth: "150px" }}
            >
              {name}
            </div>
            <Tooltip
              anchorSelect={`.tm-name-${row.original.id}`}
              content={name}
              className={"bg-dark text-white"}
            />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.role'),
      accessor: "role",
      Cell: ({ row }) => {
        const Role = row.original.role;
        const roleKey = {
          0: 'roles.admin',
          1: 'roles.admin',
          2: 'roles.moderator',
          3: 'roles.sales',
          4: 'roles.accountant',
          5: 'roles.teamMember'
        }[Role] || 'roles.moderator';

        return (
          <div className={"d-flex justify-content-center align-items-center"}>
            <div className={"shadow-sm rounded-2 p-1"}>{t(roleKey)}</div>
          </div>
        );
      },
    },
    { Header: t('tables.headers.status'), accessor: "status" },
    { Header: t('tables.headers.totalLeads'), accessor: "total_leads" },
    {
      Header: isUserExcluded(user?.user?.id)
        ? t('tables.headers.advancePaid', 'Advance Paid')
        : t('tables.headers.completed'),
      accessor: "completed_leads"
    },
    {
      Header: isUserExcluded(user?.user?.id)
        ? t('tables.headers.followUp', 'Follow Up')
        : t('tables.headers.inProgress'),
      accessor: "in_progress_leads"
    },
    {
      Header: isUserExcluded(user?.user?.id)
        ? t('tables.headers.notInterested', 'Not Interested')
        : t('tables.headers.rejected'),
      accessor: "rejected_leads"
    },
    { Header: t('tables.headers.booked'), accessor: "booked_leads" },
    { Header: t('tables.headers.assigned'), accessor: "assigned_leads" },
  ];

  const salesColumns = [
    {
      Header: "#",
      accessor: "salesId",
      Cell: ({ row }) => <Link to={`/leads/${row.original.lead_id}`}>{row.index + 1}</Link>,
    },
    {
      Header: t('tables.headers.member'),
      accessor: "team_name",
      Cell: ({ row }) => {
        const leadId = row.original.lead_id;
        const value = row.original.team_name;
        return (
          <>
            <Link
              to={`/leads/${leadId}`}
              className={`one-line sales-tm-${row.index}`}
              style={{ maxWidth: '140px' }}
            >
              {value}
            </Link>
            <Tooltip
              anchorSelect={`.sales-tm-${row.index}`}
              content={value}
              className={"bg-dark text-white"}
            />
          </>
        );
      }
    },
    {
      Header: t('tables.headers.lead'),
      accessor: "lead_name",
      Cell: ({ row }) => {
        const leadId = row.original.lead_id;
        const value = row.original.lead_name;
        return (
          <>
            <Link
              to={`/leads/${leadId}`}
              className={`one-line sales-lead-${row.original.lead_id}`}
              style={{ maxWidth: '140px' }}
            >
              {value}
            </Link>
            <Tooltip
              anchorSelect={`.sales-lead-${row.original.lead_id}`}
              content={value}
              className={"bg-dark text-white"}
            />
          </>
        );
      }
    },
    {
      Header: isUserExcluded(user?.user?.id)
        ? t('tables.headers.followUp', 'Follow Up')
        : t('tables.headers.inProgress'),
      accessor: "in_progress",
      Cell: ({ row }) => {
        const status = Number(row?.original?.lead_status);
        return (
          <>
            <Link
              to={`/leads/${row.original.lead_id}`}
              className={`one-line mx-auto sales-inprogress-${row.index}`}
              style={{ maxWidth: "200px" }}
            >
              {status === LEAD_STATUS.IN_PROGRESS ? row?.original?.Act_note : "-"}
            </Link>
            {status === LEAD_STATUS.IN_PROGRESS && (
              <Tooltip
                anchorSelect={`.sales-inprogress-${row.index}`}
                content={row?.original?.Act_note}
                className={"bg-dark text-white"}
              />
            )}
          </>
        );
      },
    },
    {
      Header: t('tables.headers.createdAt'),
      accessor: "create",
      Cell: ({ row }) => {
        const dateStr = row?.original?.Act_created_at.slice(0, -3);
        return (
          <>
            <Link
              to={`/leads/${row.original.lead_id}`}
              className={`one-line sales-created-${row.index}`}
              style={{ maxWidth: "160px" }}
            >
              {dateStr}
            </Link>
            <Tooltip
              anchorSelect={`.sales-created-${row.index}`}
              content={dateStr}
              className={"bg-dark text-white"}
            />
          </>
        );
      }
    },
    {
      Header: isUserExcluded(user?.user?.id)
        ? t('tables.headers.advancePaid', 'Advance Paid')
        : t('tables.headers.completed'),
      accessor: "completed",
      Cell: ({ row }) => {
        const status = Number(row?.original?.lead_status);
        const dateWithoutSeconds = row?.original?.Act_created_at.slice(0, -3);
        return (
          <>
            <Link
              to={`/leads/${row.original.lead_id}`}
              className={`one-line sales-completed-${row.index}`}
              style={{ maxWidth: "160px" }}
            >
              {status === LEAD_STATUS.COMPLETED ? dateWithoutSeconds : "-"}
            </Link>
            {status === LEAD_STATUS.COMPLETED && (
              <Tooltip
                anchorSelect={`.sales-completed-${row.index}`}
                content={dateWithoutSeconds}
                className={"bg-dark text-white"}
              />
            )}
          </>
        );
      },
    },
    {
      Header: isUserExcluded(user?.user?.id)
        ? t('tables.headers.notInterested', 'Not Interested')
        : t('tables.headers.rejected'),
      accessor: "rejected",
      Cell: ({ row }) => {
        const status = Number(row?.original?.lead_status);
        const dateWithoutSeconds = row?.original?.Act_created_at.slice(0, -3);
        return (
          <>
            <Link
              to={`/leads/${row.original.lead_id}`}
              className={`one-line sales-rejected-${row.index}`}
              style={{ maxWidth: "160px" }}
            >
              {status === LEAD_STATUS.REJECTED ? dateWithoutSeconds : "-"}
            </Link>
            {status === LEAD_STATUS.REJECTED && (
              <Tooltip
                anchorSelect={`.sales-rejected-${row.index}`}
                content={dateWithoutSeconds}
                className={"bg-dark text-white"}
              />
            )}
          </>
        );
      },
    },
    {
      Header: t('tables.headers.service'),
      accessor: "service",
      Cell: ({ row }) => {
        const service = row.original.service;
        return (
          <>
            <Link
              to={`/leads/${row.original.lead_id}`}
              className={`one-line sales-service-${row.index}`}
              style={{ maxWidth: "120px" }}
            >
              {service || "-"}
            </Link>
            <Tooltip
              anchorSelect={`.sales-service-${row.index}`}
              content={service || "-"}
              className={"bg-dark text-white"}
            />
          </>
        );
      },
    },
  ];

  const statisticsForTMColumns = [
    {
      Header: "#",
      accessor: "teamMemberId",
      Cell: ({ row }) => <span>{row.index + 1}</span>,
    },
    {
      Header: t('tables.headers.lead'),
      accessor: "name",
      Cell: ({ row }) => {
        const value = row.original.name;
        return (
          <>
            <div className={`one-line statistics-lead-${row.index}`} style={{ maxWidth: "150px" }}>{value}</div>
            <Tooltip anchorSelect={`.statistics-lead-${row.index}`} content={value} className={"bg-dark text-white"} />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.phone'),
      accessor: "phone",
      Cell: ({ row }) => {
        const value = row.original.phone;
        return (
          <>
            <div className={`one-line statistics-phone-${row.index}`} style={{ maxWidth: "120px" }}>{value}</div>
            <Tooltip anchorSelect={`.statistics-phone-${row.index}`} content={value} className={"bg-dark text-white"} />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.pageName'),
      accessor: "page_name",
      Cell: ({ row }) => {
        const value = row.original.page_name;
        return (
          <>
            <div className={`one-line statistics-page-${row.index}`} style={{ maxWidth: "150px" }}>{value}</div>
            <Tooltip anchorSelect={`.statistics-page-${row.index}`} content={value} className={"bg-dark text-white"} />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.formName'),
      accessor: "form_name",
      Cell: ({ row }) => {
        const value = row.original.form_name;
        return (
          <>
            <div className={`one-line statistics-form-${row.index}`} style={{ maxWidth: "150px" }}>{value}</div>
            <Tooltip anchorSelect={`.statistics-form-${row.index}`} content={value} className={"bg-dark text-white"} />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.source'),
      accessor: "source",
      Cell: ({ row }) => {
        const source = row.original.source;
        const IconComponent = sourceToIcon[source] || null;

        return (
          <div className={"mx-auto social-icon-container"}>
            {IconComponent && <ReactSVG src={IconComponent} />}
          </div>
        );
      },
    },
    {
      Header: t('tables.headers.status'),
      accessor: "status",
      Cell: ({ row }) => {
        const status = Number(row?.original?.status);
        const statusInfo = getStatusInfo(status, user, t);

        return (
          <div
            className={`status-badge ${statusInfo.className} rounded-pill p-1 fw-bold`}
            style={{ fontSize: "0.8rem" }}
          >
            {statusInfo.text}
          </div>
        );
      },
    },
    { Header: t('tables.headers.amount'), accessor: "amount" },
  ];

  const newLeadsForAdmin = [
    {
      Header: t('tables.headers.id'),
      accessor: "leadId",
      Cell: ({ row }) => <span>{row.index + 1}</span>,
    },
    {
      Header: t('tables.headers.leadName'),
      accessor: "name",
      Cell: ({ row }) => {
        const leadName = row.original.name;
        return (
          <>
            <div className={`text-nowrap overflow-hidden text-center mx-auto one-line lead-name-${row.original.id}`} style={{ maxWidth: "200px" }}>
              {leadName}
            </div>
            <Tooltip
              anchorSelect={`.lead-name-${row.original.id}`}
              content={leadName}
              className={"bg-white text-dark"}
            />
          </>
        )
      },
    },
    {
      Header: t('tables.headers.email'),
      accessor: "email",
      Cell: ({ row }) => {
        const email = row.original.email;
        return (
          <>
            <div className={`text-nowrap overflow-hidden text-center mx-auto one-line lead-email-${row.original.id}`} style={{ maxWidth: "200px" }}>
              {email}
            </div>
            <Tooltip
              anchorSelect={`.lead-email-${row.original.id}`}
              content={email}
              className={"bg-white text-dark"}
            />
          </>
        )
      },
    },
    {
      Header: t('tables.headers.phone'),
      accessor: "phone",
      Cell: ({ row }) => {
        const phone = row.original.phone;
        return (
          <>
            <div
              className={`one-line admin-phone-${row.original.id}`}
              style={{ maxWidth: "150px" }}
            >
              {phone}
            </div>
            <Tooltip
              anchorSelect={`.admin-phone-${row.original.id}`}
              content={phone}
              className={"bg-dark text-white"}
            />
          </>
        )
      },
    },
    {
      Header: t('tables.headers.source'),
      accessor: "source",
      Cell: ({ row }) => {
        const source = row.original.source;
        const IconComponent = sourceToIcon[source] || null;

        return (
          <div className={"mx-auto social-icon-container"}>
            {IconComponent && <ReactSVG src={IconComponent} />}
          </div>
        );
      },
    },
    {
      Header: t('tables.headers.createdBy'),
      accessor: "create.name",
      Cell: ({ row }) => {
        const creatorName = row.original.create?.name || '';
        return (
          <>
            <div
              className={`one-line admin-createdby-${row.original.id}`}
              style={{ maxWidth: "160px" }}
            >
              {creatorName}
            </div>
            <Tooltip
              anchorSelect={`.admin-createdby-${row.original.id}`}
              content={creatorName}
              className={"bg-dark text-white"}
            />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.status'),
      accessor: "status",
      Cell: ({ value }) => {
        const status = Number(value);
        const statusInfo = getStatusInfo(status, user, t);

        return (
          <span className={`status-badge ${statusInfo.className}`}>
            {statusInfo.text}
          </span>
        );
      }
    },
    {
      Header: t('tables.headers.createdAt'),
      accessor: "created_at",
      Cell: ({ value, row }) => {
        if (value) {
          const parsedDate = parseISO(value);
          const formattedDate = format(parsedDate, 'yyyy-MM-dd');
          return (
            <>
              <div
                className={`one-line admin-createdat-${row.original.id}`}
                style={{ maxWidth: "160px" }}
              >
                {formattedDate}
              </div>
              <Tooltip
                anchorSelect={`.admin-createdat-${row.original.id}`}
                content={formattedDate}
                className={"bg-dark text-white"}
              />
            </>
          );
        }
        return null;
      },
    }
  ];

  const ticketsColumns = (isLoadingTicketDetails, loadingTicketId, handleViewTicket) => [
    {
      Header: t('tables.headers.id'),
      accessor: "ticketId",
      Cell: ({ row }) => <span>{row.index + 1}</span>,
    },
    {
      Header: t('tables.headers.ticketTitle'),
      accessor: "title",
      Cell: ({ row }) => {
        const title = row.original.title;
        return (
          <>
            <div
              className={`text-nowrap overflow-hidden text-center mx-auto one-line ticket-title-${row.original.id}`}
              style={{ maxWidth: "200px" }}
            >
              {title}
            </div>
            <Tooltip
              anchorSelect={`.ticket-title-${row.original.id}`}
              content={title}
              className={"bg-dark text-white"}
            />
          </>
        )
      },
    },
    {
      Header: t('tables.headers.status'),
      accessor: "status",
      Cell: ({ row }) => {
        const status = row.original.status;

        // Define badge colors based on status
        const getStatusBadgeVariant = (status) => {
          switch (status?.toLowerCase()) {
            case "open":
              return "primary";
            case "in_progress":
              return "warning";
            case "closed":
            case "resolved":
              return "success";
            case "pending":
              return "info";
            case "cancelled":
              return "danger";
            default:
              return "secondary";
          }
        };

        return (
          <div className="text-center">
            <Badge
              bg={getStatusBadgeVariant(status)}
              className="px-3 py-2 d-inline-block"
              style={{ minWidth: "100px" }}
            >
              {(() => {
                // Replace underscores with spaces and capitalize each word
                if (!status) return "";
                return status
                  .replace(/_/g, " ")
                  .split(" ")
                  .map((word) =>
                    word.length > 0
                      ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                      : ""
                  )
                  .join(" ");
              })()}
            </Badge>
          </div>
        );
      },
    },
    {
      Header: t('tables.headers.priority'),
      accessor: "priority",
      Cell: ({ row }) => {
        const priority = row.original.priority;

        // Define badge colors based on priority
        const getPriorityBadgeVariant = (priority) => {
          switch (priority?.toLowerCase()) {
            case "high":
              return "danger";
            case "medium":
              return "warning";
            case "low":
              return "success";
            default:
              return "secondary";
          }
        };

        return (
          <div className="text-center">
            <Badge
              bg={getPriorityBadgeVariant(priority)}
              className="px-3 py-2 d-inline-block"
              style={{ minWidth: "100px" }}
            >
              {priority}
            </Badge>
          </div>
        );
      },
    },
    {
      Header: t('tables.headers.createdAt'),
      accessor: "created_at",
      Cell: ({ value, row }) => {
        if (value) {
          const parsedDate = parseISO(value);
          const formattedDate = format(parsedDate, 'yyyy-MM-dd');
          return (
            <>
              <div className={`one-line ticket-created-${row.index}`} style={{ maxWidth: "160px" }}>
                {formattedDate}
              </div>
              <Tooltip anchorSelect={`.ticket-created-${row.index}`} content={formattedDate} className={"bg-dark text-white"} />
            </>
          );
        }
      },
    },
    {
      Header: t('tables.headers.actions'),
      Cell: ({ row }) => {
        const isThisTicketLoading = isLoadingTicketDetails && loadingTicketId === row.original.id;

        return (
          <div className="d-flex justify-content-center">
            <Button
              variant="info"
              size="sm"
              className="view-ticket-btn"
              disabled={isThisTicketLoading}
              onClick={(e) => {
                e.stopPropagation();
                handleViewTicket(row.original.id);
              }}
            >
              {isThisTicketLoading ? (
                <Spinner animation="border" size="sm" className="me-1" />
              ) : (
                <FaEye className="me-1" />
              )}
              {t('buttons.view')}
            </Button>
          </div>
        );
      }
    }
  ];

  // new leads today table columns for the Admin dashboard
  const newLeadsTodayColumns = [
    {
      Header: t('tables.headers.id') || '#',
      accessor: 'leadId',
      Cell: ({ row }) => <span>{row.index + 1}</span>,
    },
    {
      Header: t('tables.headers.leadName') || 'Name',
      accessor: 'name',
      Cell: ({ row }) => {
        const leadName = row.original.name;
        return (
          <>
            <Link
              to={`/admin/clients/leads/profile/${row.original.id}`}
              className={`one-line newleads-name-${row.original.id}`}
              style={{ maxWidth: "180px" }}
            >
              {leadName}
            </Link>
            <Tooltip
              anchorSelect={`.newleads-name-${row.original.id}`}
              content={leadName}
              className={"bg-dark text-white"}
            />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.clientName') || 'Client',
      accessor: 'client_name',
      Cell: ({ row }) => {
        const clientName = row.original.client_name;
        const clientId = row.original.client_id;
        return (
          <>
            <Link
              to={`/admin/clients/leads/${clientId}`}
              className={`one-line newleads-client-${row.original.id}`}
              style={{ maxWidth: "180px" }}
            >
              {clientName}
            </Link>
            <Tooltip
              anchorSelect={`.newleads-client-${row.original.id}`}
              content={clientName}
              className={"bg-dark text-white"}
            />
          </>
        );
      },
    },
    {
      Header: t('tables.headers.phone'),
      accessor: 'phone',
      Cell: ({ row }) => (
        <>
          <Link
            to={`/admin/clients/leads/profile/${row.original.id}`}
            className={`one-line newleads-phone-${row.original.id}`}
            style={{ maxWidth: "150px" }}
          >
            {row.original.phone}
          </Link>
          <Tooltip
            anchorSelect={`.newleads-phone-${row.original.id}`}
            content={row.original.phone}
            className={"bg-dark text-white"}
          />
        </>
      ),
    },
    {
      Header: t('tables.headers.source'),
      accessor: 'source',
      Cell: ({ row }) => {
        const source = row.original.source;
        const IconComponent = sourceToIcon[source] || null;
        return (
          <Link to={`/admin/clients/leads/profile/${row.original.id}`} className="mx-auto social-icon-container">
            {IconComponent && <ReactSVG src={IconComponent} />}
          </Link>
        );
      },
    },
    {
      Header: t('tables.headers.status') || 'Status',
      accessor: 'status',
      Cell: ({ row }) => {
        const status = Number(row?.original?.status);
        const statusInfo = getStatusInfo(status, user, t);

        return (
          <Link
            to={`/admin/clients/leads/profile/${row.original.id}`}
            className={`status-badge ${statusInfo.className} rounded-pill p-1`}
            style={{ fontSize: '0.8rem', fontWeight: 600 }}
          >
            {statusInfo.text}
          </Link>
        );
      },
    },

  ];

  // completed leads today table columns (admin dashboard)
  const completedLeadsTodayColumns = [
    {
      Header: t('tables.headers.id') || '#',
      accessor: 'leadId',
      Cell: ({ row }) => <span>{row.index + 1}</span>,
    },
    {
      Header: t('leadsTable.columns.leadName') || 'Name',
      accessor: 'name',
      Cell: ({ row }) => (
        <>
          <Link
            to={`/admin/clients/leads/profile/${row.original.id}`}
            className={`one-line completed-name-${row.original.id}`}
            style={{ maxWidth: "180px" }}
          >
            {row.original.name}
          </Link>
          <Tooltip
            anchorSelect={`.completed-name-${row.original.id}`}
            content={row.original.name}
            className={"bg-dark text-white"}
          />
        </>
      ),
    },
    {
      Header: t('leadsTable.columns.clientName') || 'Client',
      accessor: 'client_name',
      Cell: ({ row }) => {
        const clientName = row.original.client_name;
        const clientId = row.original.client_id;
        return (
          <>
            <Link
              to={`/admin/clients/leads/${clientId}`}
              className={`one-line completed-client-${row.original.id}`}
              style={{ maxWidth: "180px" }}
            >
              {clientName}
            </Link>
            <Tooltip
              anchorSelect={`.completed-client-${row.original.id}`}
              content={clientName}
              className={"bg-dark text-white"}
            />
          </>
        );
      },
    },
    {
      Header: t('leadsTable.columns.phone'),
      accessor: 'phone',
      Cell: ({ row }) => (
        <>
          <Link
            to={`/admin/clients/leads/profile/${row.original.id}`}
            className={`one-line completed-phone-${row.original.id}`}
            style={{ maxWidth: "150px" }}
          >
            {row.original.phone}
          </Link>
          <Tooltip
            anchorSelect={`.completed-phone-${row.original.id}`}
            content={row.original.phone}
            className={"bg-dark text-white"}
          />
        </>
      ),
    },
    {
      Header: t('leadsTable.columns.assignedTo') || 'Assigned To',
      accessor: 'assignedTo',
      Cell: ({ row }) => {
        const assignedTo = row.original.assignedTo;
        return <Link to={`/admin/clients/leads/profile/${row.original.id}`}>{assignedTo?.name}</Link>;
      },
    },
    {
      Header: t('leadsTable.columns.source'),
      accessor: 'source',
      Cell: ({ row }) => {
        const source = row.original.source;
        const IconComponent = sourceToIcon[source] || null;
        return (
          <Link to={`/admin/clients/leads/profile/${row.original.id}`} className='mx-auto social-icon-container'>
            {IconComponent && <ReactSVG src={IconComponent} />}
          </Link>
        );
      },
    },
    {
      Header: t('leadsTable.columns.createdAt') || 'Created At',
      accessor: 'createdAt',
      Cell: ({ row, value }) => {
        let display = value;
        if (value) {
          const parsedDate = new Date(value);
          if (!isNaN(parsedDate.getTime())) {
            display = format(parsedDate, 'yyyy-MM-dd HH:mm:ss');
          }
        }
        const classKey = `completed-created-${row.original.id}`;
        return (
          <>
            <Link
              to={`/admin/clients/leads/profile/${row.original.id}`}
              className={`one-line ${classKey}`}
              style={{ maxWidth: "180px" }}
            >
              {display || '-'}
            </Link>
            {display && (
              <Tooltip anchorSelect={`.${classKey}`} content={display} className={"bg-dark text-white"} />
            )}
          </>
        );
      },
    },
  ];

  /*
   * Filter Table Leads Columns (All Clients Tab)
   * Accepts an options object for interactive controls that depend on the parent component.
   */
  const filterTableLeadsColumns = ({
    filteredTeamMembers = [],
    currentUserPermissions = [],
    dispatch,
    handleAssignTeamMemberThunk,
    handleDelete,
    isHoverSupported = true,
    handleTouchStart = () => { },
    tooltipVisible = {},
    user = null,
  } = {}) => [
      {
        Header: t('leadsTable.columns.id'),
        accessor: 'clientId',
        Cell: () => null, // hidden index column
      },
      {
        id: 'updatedAt',
        accessor: 'updatedAt',
        Header: 'Updated At',
        Cell: () => null,
      },
      {
        Header: t('leadsTable.columns.contactName'),
        accessor: 'contactName',
        Cell: ({ row }) => {
          const name = row.original.contactName;
          const unassigned = row.original.assignedTo === null || row.original.assignedTo === undefined;
          const leadId = row.original.id;
          return (
            <>
              <Link
                to={`/leads/${leadId}`}
                className={`d-flex align-items-center gap-1 justify-content-center one-line filter-contact-${leadId}`}
                style={{ maxWidth: '200px' }}
              >
                {unassigned && <IoSparklesSharp size={18} color="#92C020" />} {name}
              </Link>
              <Tooltip
                anchorSelect={`.filter-contact-${leadId}`}
                content={name}
                className={"bg-dark text-white"}
              />
            </>
          );
        },
      },
      {
        Header: t('leadsTable.columns.phone'),
        accessor: 'phone',
        Cell: ({ row }) => {
          const phone = row.original.phone;
          return (
            <>
              <Link
                to={`/leads/${row.original.id}`}
                className={`one-line filter-phone-${row.original.id}`}
                style={{ maxWidth: "150px" }}
              >
                {phone}
              </Link>
              <Tooltip
                anchorSelect={`.filter-phone-${row.original.id}`}
                content={phone}
                className={"bg-dark text-white"}
              />
            </>
          );
        },
      },
      {
        Header: t('leadsTable.columns.assignedTo'),
        accessor: 'assignedTo',
        Cell: ({ row }) => {
          const assignedTo = row.original.assignedTo;

          if (assignedTo) return <span>{assignedTo}</span>;

          return (
            <Dropdown>
              <Dropdown.Toggle variant="light" id="assign-dropdown" className="team-actions-button p-0 rounded-3">
                <div className="assign-client-icon m-0">
                  <FaUserPlus size={20} />
                </div>
              </Dropdown.Toggle>
              <Dropdown.Menu className="team-actions-menu" container="body">
                {filteredTeamMembers?.length > 0 ? (
                  filteredTeamMembers.map((member) => (
                    <Dropdown.Item
                      key={member.id}
                      onClick={() => dispatch && dispatch(handleAssignTeamMemberThunk({ leadId: row.original.id, memberId: member.id }))}
                    >
                      {member.name}
                    </Dropdown.Item>
                  ))
                ) : (
                  <Dropdown.Item disabled>{t('leadsTable.columns.noTeamMembers')}</Dropdown.Item>
                )}
              </Dropdown.Menu>
            </Dropdown>
          );
        },
      },
      {
        Header: t('leadsTable.columns.source'),
        accessor: 'source',
        Cell: ({ row }) => {
          const src = row.original.source;
          const IconComponent = sourceToIcon[src] || null;
          return (
            <Link to={`/leads/${row.original.id}`} className="mx-auto social-icon-container">
              {IconComponent && <ReactSVG src={IconComponent} />}
            </Link>
          );
        },
      },
      {
        Header: t('leadsTable.columns.status'),
        accessor: 'status',
        Cell: ({ row }) => {
          const status = Number(row.original.status);
          const statusInfo = getStatusInfo(status, user, t);

          return (
            <Link to={`/leads/${row.original.id}`} className={`status-badge ${statusInfo.className} rounded-pill p-1`} style={{ fontSize: '0.8rem', fontWeight: 600 }}>
              {statusInfo.text}
            </Link>
          );
        },
      },
      {
        Header: t('leadsTable.columns.service'),
        accessor: 'service',
        Cell: ({ row }) => {
          const serviceName = row.original.service;
          const leadId = row.original.id;
          return (
            <>
              <Link
                to={`/leads/${leadId}`}
                className={`one-line filter-service-${leadId}`}
                style={{ maxWidth: '150px' }}
              >
                {serviceName}
              </Link>
              <Tooltip
                anchorSelect={`.filter-service-${leadId}`}
                content={serviceName}
                className={"bg-dark text-white"}
              />
            </>
          );
        },
      },
      {
        Header: t('leadsTable.columns.pageName'),
        accessor: 'pageName',
        Cell: ({ row }) => {
          const pageName = row.original.pageName;
          const leadId = row.original.id;
          return (
            <>
              <Link
                to={`/leads/${leadId}`}
                className={`one-line filter-page-${leadId}`}
                style={{ maxWidth: '150px' }}
              >
                {pageName}
              </Link>
              <Tooltip
                anchorSelect={`.filter-page-${leadId}`}
                content={pageName}
                className={"bg-dark text-white"}
              />
            </>
          );
        },
      },
      {
        Header: t('leadsTable.columns.lastActivity'),
        accessor: 'lastActivity',
        Cell: ({ row }) => {
          const logs = Array.isArray(row.original.logs) ? row.original.logs.slice(-3) : [row.original.logs];
          const lastLog = logs[0];
          if (!lastLog) return null;

          const leadId = row.original.id;
          const tooltipId = `lastActivity_${leadId}`;

          return (
            <Link to={`/leads/${leadId}`} data-tooltip-id={tooltipId}>
              <div className="activity-button">
                {lastLog?.action === 1 && <MdAssignmentTurnedIn size={20} />}
                {lastLog?.action === 2 && <FaPhone size={20} />}
                {lastLog?.action === 3 && <FaCalendar size={20} />}
                {lastLog?.action === 4 && <FaEnvelope size={20} />}
              </div>
              <Tooltip id={tooltipId} className="logs-tooltip-container" content={
                <div className="logs-list-container">
                  {logs.map((log, idx) => (
                    <div key={idx} className="log-container">
                      <div className="d-flex justify-content-center my-2">
                        {log?.action === 1 && <MdAssignmentTurnedIn size={20} className="mainColor" />}
                        {log?.action === 2 && <FaPhone size={20} className="mainColor" />}
                        {log?.action === 3 && <FaCalendar size={20} className="mainColor" />}
                        {log?.action === 4 && <FaEnvelope size={20} className="mainColor" />}
                      </div>
                      <p className="opacity-50">{log?.result || log?.note}</p>
                    </div>
                  ))}
                </div>
              } place="left-start" events={["hover"]} />
            </Link>
          );
        },
      },
      {
        Header: t('leadsTable.columns.createdAt'),
        accessor: 'createdAt',
        Cell: ({ value, row }) => {
          const classKey = `filter-created-${row.original.id}`;
          let display = value;
          if (value) {
            const parsedDate = new Date(value);
            if (!isNaN(parsedDate.getTime())) {
              display = format(parsedDate, 'yyyy-MM-dd HH:mm:ss');
            }
          }
          return (
            <>
              <Link
                to={`/leads/${row.original.id}`}
                className={`one-line ${classKey}`}
                style={{ maxWidth: "180px" }}
              >
                {display}
              </Link>
              <Tooltip anchorSelect={`.${classKey}`} content={display} className={"bg-dark text-white"} />
            </>
          );
        },
      },
      {
        Header: t('leadsTable.columns.actions'),
        Cell: ({ row }) => (
          <div className="d-flex justify-content-center">
            {currentUserPermissions?.includes('lead-edit') && (
              <Link to={`/leads/${row.original.id}`} className="me-3 shadow-sm rounded-2 p-1">
                <MdEdit size={20} className="text-dark" />
              </Link>
            )}
            {currentUserPermissions?.includes('lead-delete') && (
              <div className="shadow-sm rounded-2 p-1">
                <PiTrashFill size={20} className="text-danger" onClick={() => handleDelete && handleDelete(row.original.id)} />
              </div>
            )}
          </div>
        ),
      },
    ];

  // Ads table columns
  const adsColumns = (handleViewLeads) => [
    {
      Header: t('adsTable.columns.id'),
      accessor: 'ad_id',
      Cell: ({ row }) => (
        <div className="text-center">
          <div
            onClick={() => handleViewLeads(row.original)}
            style={{ cursor: 'pointer' }}
          >
            {row.index + 1}
          </div>
        </div>
      ),
    },
    {
      Header: t('adsTable.columns.adName'),
      accessor: 'ad_name',
      Cell: ({ row }) => {
        const adName = row.original.ad_name;
        return (
          <div className="text-center">
            <div
              onClick={() => handleViewLeads(row.original)}
              className={`one-line ad-name-${row.original.ad_id} mx-auto`}
              style={{
                maxWidth: "200px",
                cursor: 'pointer'
              }}
            >
              {adName}
            </div>
            <Tooltip
              anchorSelect={`.ad-name-${row.original.ad_id}`}
              content={adName}
              className={"bg-dark text-white"}
            />
          </div>
        );
      },
    },
    {
      Header: t('adsTable.columns.campaignName'),
      accessor: 'campaign_name',
      Cell: ({ row }) => {
        const campaignName = row.original.campaign_name;
        return (
          <div className="text-center">
            <div
              onClick={() => handleViewLeads(row.original)}
              className={`one-line campaign-name-${row.original.ad_id} mx-auto`}
              style={{
                maxWidth: "180px",
                cursor: 'pointer'
              }}
            >
              {campaignName}
            </div>
            <Tooltip
              anchorSelect={`.campaign-name-${row.original.ad_id}`}
              content={campaignName}
              className={"bg-dark text-white"}
            />
          </div>
        );
      },
    },
    {
      Header: t('adsTable.columns.pageName'),
      accessor: 'page_name',
      Cell: ({ row }) => {
        const pageName = row.original.page_name;
        return (
          <div className="text-center">
            <div
              onClick={() => handleViewLeads(row.original)}
              className={`one-line page-name-${row.original.ad_id} mx-auto`}
              style={{
                maxWidth: "150px",
                cursor: 'pointer'
              }}
            >
              {pageName}
            </div>
            <Tooltip
              anchorSelect={`.page-name-${row.original.ad_id}`}
              content={pageName}
              className={"bg-dark text-white"}
            />
          </div>
        );
      },
    },
    {
      Header: t('adsTable.columns.leadsCount'),
      accessor: 'leads_count',
      Cell: ({ row }) => (
        <div className="text-center">
          <div
            onClick={() => handleViewLeads(row.original)}
            className="fw-bold text-primary"
            style={{ cursor: 'pointer' }}
          >
            {row.original.leads_count}
          </div>
        </div>
      ),
    },
    {
      Header: t('adsTable.columns.actions'),
      Cell: ({ row }) => (
        <div className="d-flex justify-content-center">
          <Button
            size="sm"
            className="submit-btn px-2 py-2"
            onClick={() => handleViewLeads(row.original)}
          >
            <BsEye className="me-1" />
            {t('buttons.view')}
          </Button>
        </div>
      ),
    },
  ];

  // Return all column configurations
  return {
    leadAssignmentColumns,
    teamMembersColumns,
    salesColumns,
    statisticsForTMColumns,
    newLeadsForAdmin,
    newLeadsTodayColumns,
    completedLeadsTodayColumns,
    ticketsColumns,
    filterTableLeadsColumns,
    adsColumns,
  };
};

// Hook to access translated columns easily
export const useTranslatedColumns = (user = null) => {
  const { t } = useTranslation();
  return useMemo(() => createTranslatedColumns(t, user), [t, user]);
};
