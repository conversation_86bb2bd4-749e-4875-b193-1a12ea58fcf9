import { useEffect, useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import { Card, Form, Row, Col, Button } from "react-bootstrap";
import { FaMagnifyingGlass, FaArrowLeft } from "react-icons/fa6";
import DataTableComponent from "../../components/CustomDataTable/DataTable.component";
import FetchingDataLoading from "../../components/LoadingAnimation/FetchingDataLoading";
import PaginationRecordsForReports from "../../components/Reports/PaginationRecordsForReports";
import { useTranslatedColumns } from "../../components/Reports/ColumnsForTables.module";
import campaignsService from "../../services/campaigns";
import NavigateBackComponent from "../AdminDashboard/NavigateBack.component";
import "./Campaigns.page.css";

export default function CampaignAdsPage() {
  const { t } = useTranslation();
  const { campaignId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { adsColumns } = useTranslatedColumns();
  const [loading, setLoading] = useState(false);
  const [ads, setAds] = useState([]);
  const [adsApiResponse, setAdsApiResponse] = useState(null);
  const [adsSearchTerm, setAdsSearchTerm] = useState("");
  const [selectedCampaign, setSelectedCampaign] = useState(null);

  useEffect(() => {
    if (campaignId) {
      // Check if campaign info was passed via navigation state
      const campaignInfo = location.state?.campaignInfo;
      if (campaignInfo) {
        setSelectedCampaign(campaignInfo);
      } else {
        // Fallback: set loading state and try to get info from API response
        setSelectedCampaign({ campaign_id: campaignId, campaign_name: `Loading...` });
      }

      fetchAdsForCampaign(campaignId, 1, 10);
    }
  }, [campaignId, location.state]);

  const fetchAdsForCampaign = async (campaignId, page = 1, recordsPerPageParam = 10) => {
    setLoading(true);
    try {
      const response = await campaignsService.getCampaignAdsApi(
        campaignId,
        recordsPerPageParam,
        page
      );

      if (
        (response?.status === 200 ||
          response?.success ||
          response?.message === "Success") &&
        response?.data
      ) {
        // Handle nested data structure - ads are in response.data.data
        setAds(response.data.data || []);
        setAdsApiResponse(response.data); // Store full API response for pagination

        // Only try to extract campaign info from response if we don't already have it from navigation state
        if (!location.state?.campaignInfo) {
          if (response.data.campaign_name) {
            // If the API response includes campaign information
            setSelectedCampaign({
              campaign_id: campaignId,
              campaign_name: response.data.campaign_name,
            });
          } else if (response.data.data && response.data.data.length > 0) {
            // If ads exist, we can try to get campaign info from the first ad
            const firstAd = response.data.data[0];
            if (firstAd.campaign_name) {
              setSelectedCampaign({
                campaign_id: campaignId,
                campaign_name: firstAd.campaign_name,
              });
            } else {
              // Fallback to campaign ID
              setSelectedCampaign({ campaign_id: campaignId, campaign_name: `Campaign ${campaignId}` });
            }
          } else {
            // No ads, fallback to campaign ID
            setSelectedCampaign({ campaign_id: campaignId, campaign_name: `Campaign ${campaignId}` });
          }
        }
      } else {
        setAds([]);
        setAdsApiResponse(null);
      }
    } catch (error) {
      console.error("Error fetching ads for campaign:", error);
      setAds([]);
      setAdsApiResponse(null);
    } finally {
      setLoading(false);
    }
  };

  const handleAdsSearchChange = (e) => {
    const newSearchTerm = e.target.value;
    setAdsSearchTerm(newSearchTerm);
  };

  const handleBackToCampaigns = () => {
    navigate("/campaigns");
  };

  const handleViewLeads = (ad) => {
    navigate(`/ads/${ad.ad_id}/leads`, {
      state: {
        adInfo: {
          ad_id: ad.ad_id,
          ad_name: ad.ad_name,
          campaign_name: ad.campaign_name,
          page_name: ad.page_name,
        },
      },
    });
  };

  // Pagination handlers for ads
  const handleAdsPageChange = (url) => {
    if (!url || !campaignId) return;

    // Extract page and per_page from URL
    const urlParams = new URLSearchParams(url.split("?")[1]);
    const page = parseInt(
      urlParams.get("current_page") || urlParams.get("page") || "1"
    );
    const perPage = parseInt(urlParams.get("per_page") || "10");

    fetchAdsForCampaign(campaignId, page, perPage);
  };

  const handleAdsPageSizeChange = (size) => {
    if (!campaignId) return;
    fetchAdsForCampaign(campaignId, 1, size); // Reset to first page when changing page size
  };

  // Get ads columns from the module
  const adsColumnsConfig = useMemo(
    () => adsColumns(handleViewLeads),
    [adsColumns, handleViewLeads]
  );

  // Filter ads based on search term (frontend filtering)
  const filteredAds = useMemo(() => {
    // Ensure ads is always an array
    const adsArray = Array.isArray(ads) ? ads : [];

    if (!adsSearchTerm.trim()) {
      return adsArray;
    }

    const searchLower = adsSearchTerm.toLowerCase();
    return adsArray.filter((ad) => {
      return (
        ad.ad_name?.toLowerCase().includes(searchLower) ||
        ad.page_name?.toLowerCase().includes(searchLower) ||
        ad.ad_id?.toString().includes(searchLower)
      );
    });
  }, [ads, adsSearchTerm]);

  // Since we're doing frontend filtering, we need to handle pagination for filtered data
  const data = useMemo(() => {
    return filteredAds;
  }, [filteredAds]);

  return (
    <>
      <NavigateBackComponent
        title={`${t("campaignsTable.adsForCampaign")} ${selectedCampaign?.campaign_name || ""}`}
        onBack={handleBackToCampaigns}
      />
      
      {loading ? (
        <FetchingDataLoading />
      ) : (
        <>
          <div
            className="mb-3 content-container"
            style={{
              opacity: loading ? 0.5 : 1,
            }}
          >
            <Row className="justify-content-end align-items-center">
              <Col lg={4} md={4} sm={12} className="mb-3">
                <Form.Group className="position-relative">
                  <Form.Control
                    placeholder={`${t(
                      "tableControls.placeholders.searchTable"
                    )} ${filteredAds.length} ${t(
                      "tableControls.placeholders.records"
                    )}...`}
                    value={adsSearchTerm}
                    onChange={handleAdsSearchChange}
                    className="rounded-pill"
                  />
                  <FaMagnifyingGlass
                    className="text-muted position-absolute"
                    style={{
                      right: "10px",
                      top: "50%",
                      transform: "translateY(-50%)",
                    }}
                  />
                </Form.Group>
              </Col>
            </Row>

            <DataTableComponent
              columns={adsColumnsConfig}
              data={data || []}
              loading={loading}
              initialSortBy={[]}
              hiddenColumns={[]}
              noDataFound={t("adsTable.noAdsFound") || "No Ads Found"}
            />

            <PaginationRecordsForReports
              onPageChange={handleAdsPageChange}
              links={adsApiResponse?.links || []}
              handlePageSizeChange={handleAdsPageSizeChange}
              per_page={adsApiResponse?.per_page || 10}
              to={adsApiResponse?.to || 0}
              total={adsApiResponse?.total || 0}
              currentPage={adsApiResponse?.current_page || 1}
            />
          </div>
        </>
      )}
    </>
  );
}
