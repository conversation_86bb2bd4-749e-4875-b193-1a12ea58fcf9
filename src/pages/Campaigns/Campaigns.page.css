.content-container {
  padding: 20px;
  margin: 0 auto;
  max-width: 100%;
}

.view-campaign-ads-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  font-size: 0.875rem;
}

.one-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

.campaign-name-cell {
  max-width: 200px;
}

.campaign-status-active {
  color: #28a745;
  font-weight: bold;
}

.campaign-status-paused {
  color: #ffc107;
  font-weight: bold;
}

.campaign-status-inactive {
  color: #dc3545;
  font-weight: bold;
}

.insights-metrics {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 0.875rem;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-weight: 500;
  color: #6c757d;
}

.metric-value {
  font-weight: bold;
  color: #495057;
}

@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }
  
  .view-campaign-ads-btn {
    font-size: 0.75rem;
    padding: 2px 6px;
  }
  
  .insights-metrics {
    font-size: 0.75rem;
  }
}
