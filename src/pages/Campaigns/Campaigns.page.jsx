import React, { useEffect, useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { Card, Form, Row, Col } from "react-bootstrap";
import { FaMagnifyingGlass } from "react-icons/fa6";
import DataTableComponent from "../../components/CustomDataTable/DataTable.component";
import FetchingDataLoading from "../../components/LoadingAnimation/FetchingDataLoading";
import PaginationRecordsForReports from "../../components/Reports/PaginationRecordsForReports";
import { useTranslatedColumns } from "../../components/Reports/ColumnsForTables.module";
import campaignsService from "../../services/campaigns";
import "./Campaigns.page.css";

export default function CampaignsPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { campaignsColumns } = useTranslatedColumns();
  const [loading, setLoading] = useState(false);
  const [campaigns, setCampaigns] = useState([]);
  const [campaignsApiResponse, setCampaignsApiResponse] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");

  // Effect to fetch campaigns when pagination changes
  useEffect(() => {
    fetchCampaigns();
  }, [currentPage, recordsPerPage]);

  const fetchCampaigns = async () => {
    setLoading(true);
    try {
      const response = await campaignsService.getAllCampaignsApi(
        recordsPerPage,
        currentPage
      );

      if (
        (response?.success || response?.message === "Success") &&
        response?.data
      ) {
        // Handle nested data structure - campaigns are in response.data.data
        setCampaigns(response.data.data || []);
        setCampaignsApiResponse(response.data); // Store full API response for pagination
      } else {
        setCampaigns([]);
        setCampaignsApiResponse(null);
      }
    } catch (error) {
      console.error("Error fetching campaigns:", error);
      setCampaigns([]);
      setCampaignsApiResponse(null);
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (e) => {
    const newSearchTerm = e.target.value;
    setSearchTerm(newSearchTerm);
    // Reset to first page when searching (frontend filtering)
    setCurrentPage(1);
  };

  const handleViewCampaignAds = (campaign) => {
    navigate(`/campaigns/${campaign.campaign_id}/ads`, {
      state: {
        campaignInfo: {
          campaign_id: campaign.campaign_id,
          campaign_name: campaign.campaign_name,
          start_date: campaign.start_date,
          status: campaign.status,
        },
      },
    });
  };

  // Pagination handlers for campaigns
  const handleCampaignsPageChange = (url) => {
    if (!url) return;

    // Extract page and per_page from URL
    const urlParams = new URLSearchParams(url.split("?")[1]);
    const page = parseInt(
      urlParams.get("current_page") || urlParams.get("page") || "1"
    );
    const perPage = parseInt(urlParams.get("per_page") || recordsPerPage);

    setCurrentPage(page);
    setRecordsPerPage(perPage);
  };

  const handleCampaignsPageSizeChange = (size) => {
    setRecordsPerPage(size);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  // Get campaigns columns from the module
  const campaignsColumnsConfig = useMemo(
    () => campaignsColumns(handleViewCampaignAds),
    [campaignsColumns, handleViewCampaignAds]
  );

  // Filter campaigns based on search term (frontend filtering)
  const filteredCampaigns = useMemo(() => {
    // Ensure campaigns is always an array
    const campaignsArray = Array.isArray(campaigns) ? campaigns : [];

    if (!searchTerm.trim()) {
      return campaignsArray;
    }

    const searchLower = searchTerm.toLowerCase();
    return campaignsArray.filter((campaign) => {
      return (
        campaign.campaign_name?.toLowerCase().includes(searchLower) ||
        campaign.status?.toLowerCase().includes(searchLower) ||
        campaign.campaign_id?.toString().includes(searchLower)
      );
    });
  }, [campaigns, searchTerm]);

  // Since we're doing frontend filtering, we need to handle pagination for filtered data
  const data = useMemo(() => {
    return filteredCampaigns;
  }, [filteredCampaigns]);

  return (
    <>
      <h3 className="my-4">{t("campaignsTable.title")}</h3>
      {loading ? (
        <FetchingDataLoading />
      ) : (
        <>
          <div
            className="mb-3 content-container"
            style={{
              opacity: loading ? 0.5 : 1,
            }}
          >
            <Row className="justify-content-end align-items-center">
              <Col lg={4} md={4} sm={12} className="mb-3">
                <Form.Group className="position-relative">
                  <Form.Control
                    placeholder={`${t(
                      "tableControls.placeholders.searchTable"
                    )} ${filteredCampaigns.length} ${t(
                      "tableControls.placeholders.records"
                    )}...`}
                    value={searchTerm}
                    onChange={handleSearchChange}
                    className="rounded-pill"
                  />
                  <FaMagnifyingGlass
                    className="text-muted position-absolute"
                    style={{
                      right: "10px",
                      top: "50%",
                      transform: "translateY(-50%)",
                    }}
                  />
                </Form.Group>
              </Col>
            </Row>

            <DataTableComponent
              columns={campaignsColumnsConfig}
              data={data || []}
              loading={loading}
              initialSortBy={[]}
              hiddenColumns={[]}
              noDataFound={t("campaignsTable.noCampaignsFound") || "No Campaigns Found"}
            />

            <PaginationRecordsForReports
              onPageChange={handleCampaignsPageChange}
              links={campaignsApiResponse?.links || []}
              handlePageSizeChange={handleCampaignsPageSizeChange}
              per_page={campaignsApiResponse?.per_page || recordsPerPage}
              to={campaignsApiResponse?.to || 0}
              total={campaignsApiResponse?.total || 0}
              currentPage={campaignsApiResponse?.current_page || currentPage}
            />
          </div>
        </>
      )}
    </>
  );
}
