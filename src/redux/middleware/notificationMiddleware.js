import { playNotificationSound } from '../../utils/notificationSound';
import { resetNotificationSound } from '../features/metaBusinessChatSlice';

/**
 * Middleware to handle notification sounds for comments
 */
export const notificationMiddleware = (store) => (next) => (action) => {
    // Call the next middleware/reducer first
    const result = next(action);

    // Check if we should play notification sound after state update
    const state = store.getState();
    if (state.metaBusinessSuite.shouldPlayNotificationSound) {
        console.log('🔔 [MIDDLEWARE] Notification sound flag is true, playing sound...');

        // Play the notification sound
        playNotificationSound();

        // Reset the flag
        store.dispatch(resetNotificationSound());

        console.log('🔔 [MIDDLEWARE] Notification sound played and flag reset');
    }

    return result;
};
