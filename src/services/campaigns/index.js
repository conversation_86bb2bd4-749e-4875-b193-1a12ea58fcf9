import apiRequest from "../../utils/apiRequest";

// Get all campaigns
const getAllCampaignsApi = async (records = 10, currentPage = 1) => {
    const params = new URLSearchParams({
        per_page: records,
        current_page: currentPage,
    });

    return await apiRequest(`campaigns?${params.toString()}`, "get");
};

// Get ads for a specific campaign
const getCampaignAdsApi = async (campaignId, records = 10, currentPage = 1) => {
    const params = new URLSearchParams({
        per_page: records,
        current_page: currentPage,
    });

    return await apiRequest(`campaigns/${campaignId}/ads?${params.toString()}`, "get");
};

export default {
    getAllCampaignsApi,
    getCampaignAdsApi,
};
