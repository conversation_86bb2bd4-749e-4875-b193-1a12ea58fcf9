/**
 * Utility for playing notification sounds
 */

// Create audio context for notification sounds
let audioContext = null;
let notificationBuffer = null;

// Initialize audio context
const initAudioContext = () => {
    if (!audioContext) {
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
    }
    return audioContext;
};

// Generate a simple notification beep sound
const generateNotificationSound = async () => {
    const context = initAudioContext();
    const sampleRate = context.sampleRate;
    const duration = 0.3; // 300ms
    const frequency = 800; // 800Hz tone

    const buffer = context.createBuffer(1, sampleRate * duration, sampleRate);
    const data = buffer.getChannelData(0);

    for (let i = 0; i < buffer.length; i++) {
        const t = i / sampleRate;
        // Create a pleasant notification sound with fade in/out
        const envelope = Math.sin(Math.PI * t / duration);
        data[i] = Math.sin(2 * Math.PI * frequency * t) * envelope * 0.3;
    }

    return buffer;
};

// Play notification sound
export const playNotificationSound = async () => {
    try {
        console.log('🔊 [SOUND] Attempting to play notification sound...');

        // Check if user has enabled sound notifications (you can add a setting for this)
        const soundEnabled = localStorage.getItem('commentSoundEnabled') !== 'false';
        if (!soundEnabled) {
            console.log('🔇 [SOUND] Sound disabled by user preference');
            return;
        }

        const context = initAudioContext();
        console.log('🎵 [SOUND] Audio context state:', context.state);

        // Resume audio context if it's suspended (required by some browsers)
        if (context.state === 'suspended') {
            console.log('▶️ [SOUND] Resuming suspended audio context...');
            await context.resume();
        }

        // Generate sound buffer if not already created
        if (!notificationBuffer) {
            console.log('🎼 [SOUND] Generating notification sound buffer...');
            notificationBuffer = await generateNotificationSound();
        }

        // Play the sound
        const source = context.createBufferSource();
        source.buffer = notificationBuffer;
        source.connect(context.destination);
        source.start();

        console.log('✅ [SOUND] Notification sound played successfully');

    } catch (error) {
        console.warn('❌ [SOUND] Could not play notification sound:', error);
    }
};

// Enable/disable sound notifications
export const setSoundEnabled = (enabled) => {
    localStorage.setItem('commentSoundEnabled', enabled.toString());
};

// Check if sound is enabled
export const isSoundEnabled = () => {
    return localStorage.getItem('commentSoundEnabled') !== 'false';
};
